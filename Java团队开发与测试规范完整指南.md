# Java团队开发完整指南

## 📋 目录
- [代码编写强制性规范](#代码编写强制性规范)
- [四层架构开发模式](#四层架构开发模式)
- [RESTful API设计规范](#restful-api设计规范)
- [Java8+新特性使用规范](#java8新特性使用规范)
- [Hutool工具类使用规范](#hutool工具类使用规范)
- [阿里巴巴开发手册补充规范](#阿里巴巴开发手册补充规范)
- [技术栈使用标准](#技术栈使用标准)
- [开发流程检查清单](#开发流程检查清单)

---

## 🔥 代码编写强制性规范

### 1. 构造函数依赖注入（强制）
**规则**：必须使用构造函数注入，保证依赖的不可变性和明确性，严禁字段注入，使用lombok的@RequiredArgsConstructor注解搭配final注入

```java
// ✅ 正确 - 构造函数注入
@RequiredArgsConstructor
public class OAuth2Controller {
    private final OAuth2Service oAuth2Service;
}

// ❌ 错误 - 字段注入
@Service
public class UserServiceImpl implements UserService {
    @Autowired
    private UserRepository userRepository;
}

// ❌ 错误 - Setter注入
@Service
public class UserServiceImpl implements UserService {
    private UserRepository userRepository;
    
    @Autowired
    public void setUserRepository(UserRepository userRepository) {
        this.userRepository = userRepository;
    }
}
```

### 2. 私有化常量
**规则**：类级别的常量定义，必须使用 `private static final` 修饰，命名使用全大写和下划线

```java
// ✅ 正确
private static final String USER_STATUS_ACTIVE = "active";
private static final List<String> ALLOWED_ROLES = List.of("admin", "user", "guest");
private static final int DEFAULT_TIMEOUT = 30;
private static final BigDecimal MAX_AMOUNT = new BigDecimal("999999.99");

// ❌ 错误
public static final String USER_STATUS_ACTIVE = "active";
String USER_STATUS_ACTIVE = "active";
```

### 3. 命名清晰性
**规则**：所有变量、方法、类的名称必须清晰、准确地反映其用途，做到"望文生义"

```java
// ✅ 正确
public BigDecimal calculateInsurancePremium(PolicyData policyData) { }
public Optional<User> findUserByEmailAndStatus(String email, UserStatus status) { }

// ❌ 错误
public List<User> getData(String param) { }
public BigDecimal calc(PolicyData data) { }
public Optional<User> find(String str, Object obj) { }
```

### 4. 禁止反射滥用
**规则**：严格限制反射的使用，99%的属性访问都应通过getter/setter方法

```java
// ✅ 正确
user.getEmail();
broker.getUserId();
order.getCustomer().getName();

// ❌ 禁止（除非框架必需）
Field field = user.getClass().getDeclaredField("email");
field.setAccessible(true);
String email = (String) field.get(user);
```

### 5. 避免不必要的中间变量
**规则**：避免创建仅使用一次的中间变量，应直接返回表达式结果

```java
// ✅ 正确
return ApiResponse.success(validateCodeService.createCaptcha());
return userRepository.findById(userId)
    .map(UserResponse::fromEntity)
    .orElseThrow(() -> new BusinessException("用户不存在: " + userId));

// ❌ 错误
String captcha = validateCodeService.createCaptcha();
return ApiResponse.success(validateCodeService.createCaptcha(captcha));

Optional<User> userOpt = userRepository.findById(userId);
UserResponse response = userOpt.map(UserResponse::fromEntity)
    .orElseThrow(() -> new BusinessException("用户不存在: " + userId));
return response;
```

### 6. Stream API优先
**规则**：集合操作优先使用Stream API，提高代码可读性和函数式编程风格

```java
// ✅ 正确
List<String> activeUserEmails = users.stream()
                .filter(User::isActive)                   // 过滤活跃用户
                .map(User::getEmail)                      // 提取邮箱
                .filter(email -> email != null && !email.isEmpty()) // 过滤空邮箱
                .distinct()                               // 去重
                .sorted()                                 // 排序
                .collect(Collectors.toList());            // 收集为列表

// ❌ 错误（简单场景不用传统循环）
List<Long> userIds = new ArrayList<>();
for (User user : users) {
    if (user.isActive()) {
        userIds.add(user.getId());
    }
}
return userIds;
```

### 7. 链式调用与Builder模式
**规则**：对于复杂对象构建和查询，优先使用链式调用，检查是否存在可以合并为链式调用的多个顺序操作，如果可以就一定要进行链式调用

```java
// ✅ 正确
StringBuilder sb = new StringBuilder()
                .append("Hello")
                .append(" ")
                .append("World")
                .append("!")
                .reverse();

User user = User.builder()
    .name(request.getName())
    .email(request.getEmail())
    .status(UserStatus.ACTIVE)
    .createdAt(LocalDateTime.now())
    .build();
```

### 8. 禁止N+1查询
**规则**：严格审查循环内部，确保没有数据库查询操作，使用JOIN FETCH或批量查询

```java
// ✅ 正确
@Query("SELECT u FROM User u JOIN FETCH u.role WHERE u.status = :status")
List<User> findActiveUsersWithRoles(@Param("status") UserStatus status);

// ✅ 正确 - 批量查询
List<Long> userIds = users.stream().map(User::getId).collect(Collectors.toList());
Map<Long, Role> roleMap = roleRepository.findByUserIdIn(userIds)
    .stream()
    .collect(Collectors.toMap(Role::getUserId, Function.identity()));

// ❌ 错误
List<User> users = userRepository.findByStatus("active");
for (User user : users) {
    Role role = roleRepository.findByUserId(user.getId()); // N+1查询
}
```
### 9. 常量类管理
**规则**：将常量定义到专门的常量类中，不允许出现魔法值

```java
// ✅ 正确 - 常量类
public final class UserConstants {
    private UserConstants() {}
    
    public static final String STATUS_ACTIVE = "active";
    public static final String STATUS_INACTIVE = "inactive";
    public static final int DEFAULT_PAGE_SIZE = 20;
    public static final int MAX_PAGE_SIZE = 100;
}

// ✅ 正确 - 使用常量
if (UserConstants.STATUS_ACTIVE.equals(user.getStatus())) {
    // 业务逻辑
}

// ❌ 错误 - 魔法值
if ("active".equals(user.getStatus())) {
    // 业务逻辑
}
```

### 10. 异常处理规范
**规则**：不允许空catch块，必须记录异常日志或重新抛出

```java
// ✅ 正确
try {
    userService.createUser(request);
} catch (DuplicateUserException e) {
    log.warn("用户已存在: {}", request.getEmail(), e);
    throw new BusinessException("用户邮箱已被注册");
} catch (Exception e) {
    log.error("创建用户失败: {}", request, e);
    throw new SystemException("系统异常，请稍后重试");
}

// ❌ 错误 - 空catch块
try {
    userService.createUser(request);
} catch (Exception e) {
    // 什么都不做
}
```
### 11. 不允许写单元测试
**规则**：写的每一个类或者接口都不要生成对应的单元测试，我会自己测试

### 12. DRY原则 (Don't Repeat Yourself)
**规则**：仔细避免重复的代码块，将其提取到一个独立的、可复用的函数或方法中,如果开发过程中发现又可以提取到公共方法的时候，需要进行提取。

### 13. 拒绝无意义的注释
**规则**：检查并避免那些只解释了代码字面意思的"废话注释"

### 14. Lombok注解使用
**规则**：可以用lombok注解的地方就是用lombok，比如@NoArgsConstructor @AllArgsConstructor等等，减少样板代码的开发

### 15. switch语法使用：
**规则**：尽量避免多个else if，如果需要多重判断，则使用switch case语句，比如:
```
case SUPPLIER -> {
                handleSupplierEdits(claim, requestDto);
                claim.setSupplierLatestReplyDate(LocalDate.now());
                if (ObjectUtil.isNull(claim.getSupplierFirstReplyDate())) {
                    claim.setSupplierFirstReplyDate(LocalDate.now());
                }
                switch (requestDto.getSupplierFeedback()) {
                    case SUPPLIER_FEEDBACK_ACCEPT -> claim.setProcessStatus(ProcessStatusEnum.CLAIM_APPROVED.name());
                    case SUPPLIER_FEEDBACK_REFUSE -> claim.setProcessStatus(ProcessStatusEnum.CLAIM_REJECTED.name());
                    case SUPPLIER_FEEDBACK_INSUFFICIENT_MATERIALS ->
                            claim.setProcessStatus(ProcessStatusEnum.MATERIALS_REQUIRED.name());
                    default -> {
                    }
                }

            }
            default -> {
            }
```

---

## 🏗️ 四层架构开发模式

### 架构层次与职责
```
Controller层 (Web层) - HTTP交互、参数校验、响应封装
    ↓
DTO层 (数据传输对象) - 请求/响应数据结构
    ↓
Service层 (业务逻辑层) - 核心业务逻辑、事务管理
    ↓
Repository层 (数据访问层) - 数据持久化操作
```

### 1. Controller层规范
**职责**：仅负责HTTP交互、基础参数校验、调用Service、封装响应。严禁包含任何业务逻辑


### 2. DTO层规范
**职责**：定义请求/响应的数据结构。必须与Entity分离



### 3. Service层规范
**职责**：实现核心业务逻辑，保持接口与实现分离



### 4. Entity/Model层规范
**职责**：映射数据库表。严禁直接在Controller层接收请求或返回响应

```java
@Entity
@Table(name = "users")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class User extends EntityBase {
    

    
    @Column(nullable = false, length = 50)
    private String name;
    
    @Column(nullable = false, unique = true, length = 100)
    private String email;
    
    @Column(length = 20)
    private String phone;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private UserRole role;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private UserStatus status;
    

    
  
}

@Setter
@Getter
@MappedSuperclass
@NoArgsConstructor
@EntityListeners(AuditingEntityListener.class)
abstract class EntityBase {
    @Id
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @CreatedDate
    private LocalDateTime createdAt;

    @LastModifiedDate
    private LocalDateTime updatedAt;

    @CreatedBy
    private String createdBy;

    @LastModifiedBy
    private String updatedBy;
}

```


---

## 🌐 RESTful API设计规范

### 1. 资源命名规范
```java
// ✅ 正确 - 使用复数名词
@RequestMapping("/api/users")
@RequestMapping("/api/orders")
@RequestMapping("/api/products")

// ❌ 错误 - 使用单数或动词
@RequestMapping("/api/user")
@RequestMapping("/api/getUsers")
@RequestMapping("/api/createOrder")
```

### 2. HTTP方法使用规范
```java
// GET - 查询资源
@GetMapping("/api/users")           // 获取用户列表
@GetMapping("/api/users/{id}")      // 获取单个用户

// POST - 创建资源
@PostMapping("/api/users")          // 创建用户，返回201 Created

// PUT - 完全更新资源
@PutMapping("/api/users/{id}")      // 完全更新用户，资源不存在返回404

// PATCH - 部分更新资源
@PatchMapping("/api/users/{id}")    // 部分更新用户

// DELETE - 删除资源
@DeleteMapping("/api/users/{id}")   // 删除用户，返回204 No Content
```

### 3. 参数校验规范
**规则**：必须在Controller层或Service层入口处，使用jakarta.validation注解对DTO进行校验
如果可以采用group valid 则采用group valid

```java
@RestController
@RequestMapping("/api/users")
@Validated
public class UserController {
    
    @PostMapping
    public ApiResponse<UserResponse> createUser(
            @Valid @RequestBody CreateUserRequest request) {
        // @Valid触发DTO内部的校验注解
        UserResponse user = userService.createUser(request);
        return ApiResponse.success(user);
    }
    
    @GetMapping("/{id}")
    public ApiResponse<UserResponse> getUserById(
            @PathVariable @Min(value = 1, message = "用户ID必须大于0") Long id) {
        // @Min直接校验路径参数
        UserResponse user = userService.getUserById(id);
        return ApiResponse.success(user);
    }
    
    @GetMapping
    public ApiResponse<Page<UserResponse>> getUsers(
            @Valid UserQueryParams params) {
        // 校验查询参数
        Page<UserResponse> users = userService.getUsers(params);
        return ApiResponse.success(users);
    }
}

// DTO中的校验注解
public class CreateUserRequest(
    @NotBlank(message = "用户名不能为空")
    @Size(min = 2, max = 50, message = "用户名长度必须在2-50之间")
    String name,
    
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100")
    String email,
    
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    String phone,
    
    @NotNull(message = "角色不能为空")
    UserRole role
) { }

@Data
public class AddAndEditVehicleRequest {
    public interface Edit{}
    public interface Add{}

    @NotBlank(message = "VehicleId cannot be empty!",groups = {Edit.class})
    private String id;


    @NotBlank(message = "PlateNumber cannot be empty!", groups = {Add.class, Edit.class})
    @Pattern(regexp = "^[\\u4e00-\\u9fa5]{1}[A-Z]{1}[A-Z_0-9]{5,6}$",
            message = "The license plate number does not meet the specifications",
            groups = {Add.class, Edit.class})
    private String plateNumber;


    @NotBlank(message = "VehicleLength cannot be empty!", groups = {Add.class, Edit.class})
    @Pattern(regexp = "^[1-9]\\d*(\\.\\d)?$|^[1-9]\\d*\\.\\d$",
            message = "VehicleLength must be greater than 0 and one decimal place after the decimal point",
            groups = {Add.class, Edit.class})
    private String vehicleLength;

    @NotNull(message = "IsDefault cannot be empty!", groups = {Add.class, Edit.class})
    private Boolean isDefault;
}
```

### 4. 全局异常处理规范
**规则**：必须使用@RestControllerAdvice定义全局异常处理器，严禁在Controller或Service中捕获并消化异常
抛出异常时，优先使用自定义业务异常，比如：
```java
public class BusinessException extends RuntimeException {

    @Getter
    private int code;

    @Getter
    private String message;

    @Getter
    private Object errorData;

    @Getter
    private String[] messageParams;

    public BusinessException (ExceptionEnum exceptionEnum) {
        this.code = exceptionEnum.getCode();
        this.message = exceptionEnum.getMessage();
    }


    public BusinessException (ExceptionEnum exceptionEnum, Object errorData, String... messageParams) {
        this.code = exceptionEnum.getCode();
        this.message = exceptionEnum.getMessage();
        this.errorData = errorData;
        this.messageParams = messageParams;
    }
}

public enum ExceptionEnum {
    /**
     * 测试枚举
     */
    USERNAME_OR_PASSWORD_IS_NULL(001, "用户名或密码不能为空！"),
    PASSWORD_NOT_IN_SPECIFIED_RANGE(002, "用户密码不在指定范围！"),
    USERNAME_NOT_IN_SPECIFIED_RANGE(003, "用户名不在指定范围！"),
    USERNAME_NOT_EXIST(004, "登录用户: %s 不存在！"),
    USERNAME_DEACTIVATE(005, "登录用户: %s 已停用！"),
    USERNAME_OR_PASSWORD_INCORRECT(006, "用户名或密码不正确！错误第%s次，超过%s次将锁定账户%s分钟"),
    CURRENT_USERNAME_IS_NULL(007, "当前登录人为空！"),
    CIPHERTEXT_INCORRECT_FORMATTING(010, "密文格式不正确！"),
    CAPTCHA_IS_NULL(011, "验证码不能为空！"),
    CAPTCHA_IS_LAPSE(012, "验证码已失效！"),
    CAPTCHA_IS_ERROR(013, "验证码错误！"),
    USERNAME_LOCK(014, "密码输入错误超过%s次，帐户锁定%s分钟"),
    FILE_URL_IS_NULL(015, "文件路径为空"),
    DB_FILE_URL_IS_NULL(015, "文件路径不存在:%s"),
    FILE_IS_NULL(016, "上传文件为空"),
    EXCEL_TEMPLATE_ERROR(017, "模板对比错误, 请检查并重新上传"),
    EXCEL_FILE_CONTENT_EMPTY(020, "上传的excel文件内容为空"),
    FILE_CONTENT_VERIFICATION_FAILED(021, "文件内容校验不通过,请检查并重新上传");



    @Getter
    private Integer code;
    @Getter
    private String message;

    ExceptionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
```



---

## ☕ Java8+新特性使用规范

### 1. LocalDateTime时间处理（强制）
**规则**：优先使用LocalDateTime、LocalDate、LocalTime，避免使用Date和Calendar

```java
// ✅ 正确 - 使用LocalDateTime
@Entity
public class User {
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "birth_date")
    private LocalDate birthDate;
    
    @Column(name = "login_time")
    private LocalTime loginTime;
}

// Service中的时间处理
@Service
public class UserServiceImpl implements UserService {
    
    public UserResponse createUser(CreateUserRequest request) {
        User user = User.builder()
            .name(request.name())
            .email(request.email())
            .createdAt(LocalDateTime.now())
            .build();
        
        // 时间计算
        LocalDateTime expireTime = LocalDateTime.now().plusDays(30);
        LocalDate nextMonth = LocalDate.now().plusMonths(1);
        
        return UserResponse.fromEntity(userRepository.save(user));
    }
    
    public List<UserResponse> getRecentUsers(Integer days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(
            Optional.ofNullable(days).orElse(7)
        );
        
        return userRepository.findByCreatedAtAfter(startTime)
            .stream()
            .map(UserResponse::fromEntity)
            .collect(Collectors.toList());
    }
}

// ❌ 错误 - 使用Date
private Date createdAt;
private Date updatedAt;
```

### 2. Optional判空处理（强制）
**规则**：使用Optional处理可能为null的值，避免直接使用null判断
但是并不是所有的判空都用Optional，需要根据不同情况使用，即使不用Optional，判空也需要使用包装类中的判空工具，比如Hutool包中的判空，不要用=null这种方式，比如：
```java
        
                if (StrUtil.isNotBlank(reportReqDto.getVerbatimPartKeywords())) {
                    predicates.add(criteriaBuilder.like(root.get(ISSUE_PART_KEYWORDS), "%" + reportReqDto.getVerbatimPartKeywords() + "%"));
                }

                if (StrUtil.isNotBlank(reportReqDto.getVerbatimSymptomKeyword())) {
                    predicates.add(criteriaBuilder.like(root.get(ISSUE_SYMPTOM_KEYWORDS), "%" + reportReqDto.getVerbatimSymptomKeyword() + "%"));
                }
```
```java
// ✅ 正确 - 使用Optional
@Service
public class UserServiceImpl implements UserService {
    
    public UserResponse getUserById(Long id) {
        return userRepository.findById(id)
            .map(UserResponse::fromEntity)
            .orElseThrow(() -> new UserNotFoundException("用户不存在: " + id));
    }
    
    public Optional<UserResponse> findUserByEmail(String email) {
        return userRepository.findByEmail(email)
            .map(UserResponse::fromEntity);
    }
    
    
    public List<UserResponse> getUsersByRole(UserRole role) {
        return Optional.ofNullable(role)
            .map(r -> userRepository.findByRole(r))
            .orElse(Collections.emptyList())
            .stream()
            .map(UserResponse::fromEntity)
            .collect(Collectors.toList());
    }
}

// ❌ 错误 - 直接null判断
public UserResponse getUserById(Long id) {
    User user = userRepository.findById(id).orElse(null);
    if (user == null) {
        throw new UserNotFoundException("用户不存在: " + id);
    }
    return UserResponse.fromEntity(user);
}

public void updateUser(Long id, UpdateUserRequest request) {
    User user = userRepository.findById(id).orElse(null);
    if (request.name() != null) {
        user.setName(request.name());
    }
    if (request.phone() != null) {
        user.setPhone(request.phone());
    }
}
```

### 3. Stream API集合处理（强制）
**规则**：集合操作优先使用Stream API，提高代码可读性

```java
// ✅ 正确 - 使用Stream API
@Service
public class UserServiceImpl implements UserService {
    
    public List<UserResponse> getActiveUsers() {
        return userRepository.findAll()
            .stream()
            .filter(User::isActive)
            .map(UserResponse::fromEntity)
            .collect(Collectors.toList());
    }
    
    public Map<UserRole, List<UserResponse>> getUsersByRole() {
        return userRepository.findAll()
            .stream()
            .filter(User::isActive)
            .collect(Collectors.groupingBy(
                User::getRole,
                Collectors.mapping(UserResponse::fromEntity, Collectors.toList())
            ));
    }
    
    public Optional<UserResponse> findUserWithHighestId() {
        return userRepository.findAll()
            .stream()
            .max(Comparator.comparing(User::getId))
            .map(UserResponse::fromEntity);
    }
    
    public BigDecimal calculateTotalAmount(List<Order> orders) {
        return orders.stream()
            .filter(order -> order.getStatus() == OrderStatus.COMPLETED)
            .map(Order::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
```

### 4. 函数式接口使用
```java
// ✅ 正确 - 使用函数式接口
@Service
public class UserServiceImpl implements UserService {
    
    // 使用Function进行数据转换
    private final Function<User, UserResponse> userToResponse = UserResponse::fromEntity;
    
    // 使用Predicate进行条件判断
    private final Predicate<User> isActiveUser = User::isActive;
    private final Predicate<User> isAdminUser = user -> UserRole.ADMIN.equals(user.getRole());
    
    public List<UserResponse> getActiveAdminUsers() {
        return userRepository.findAll()
            .stream()
            .filter(isActiveUser.and(isAdminUser))
            .map(userToResponse)
            .collect(Collectors.toList());
    }
    
    // 使用Consumer进行操作
    public void processUsers(List<User> users, Consumer<User> processor) {
        users.stream()
            .filter(isActiveUser)
            .forEach(processor);
    }
}
```

---

## 🔧 Hutool工具类使用规范

### 1. 字符串工具类使用（强制）
**规则**：使用Hutool的StrUtil替代原生字符串判断，避免使用== null

```java
import cn.hutool.core.util.StrUtil;

// ✅ 正确 - 使用Hutool工具类
@Service
public class UserServiceImpl implements UserService {
    
    public UserResponse createUser(CreateUserRequest request) {
        // 字符串非空判断
        if (StrUtil.isBlank(request.name())) {
            throw new IllegalArgumentException("用户名不能为空");
        }
        
        // 字符串处理
        String processedName = StrUtil.trim(request.name());
        String email = StrUtil.lowerFirst(request.email());
        
        User user = User.builder()
            .name(processedName)
            .email(email)
            .build();
        
        return UserResponse.fromEntity(userRepository.save(user));
    }
    
    public List<UserResponse> searchUsers(String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return Collections.emptyList();
        }
        
        String searchTerm = StrUtil.format("{}%", StrUtil.trim(keyword));
        return userRepository.findByNameLike(searchTerm)
            .stream()
            .map(UserResponse::fromEntity)
            .collect(Collectors.toList());
    }
}

// ❌ 错误 - 使用原生判断
if (request.name() == null || request.name().trim().isEmpty()) {
    throw new IllegalArgumentException("用户名不能为空");
}

if (keyword == null || "".equals(keyword.trim())) {
    return Collections.emptyList();
}
```

### 2. 集合工具类使用
```java
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;

@Service
public class UserServiceImpl implements UserService {
    
    public List<UserResponse> getUsersByIds(List<Long> userIds) {
        // 集合非空判断
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        
        // 去重处理
        List<Long> distinctIds = CollUtil.distinct(userIds);
        
        return userRepository.findAllById(distinctIds)
            .stream()
            .map(UserResponse::fromEntity)
            .collect(Collectors.toList());
    }
    
    public void batchUpdateUsers(List<UpdateUserRequest> requests) {
        if (CollUtil.isEmpty(requests)) {
            return;
        }
        
        // 分批处理
        List<List<UpdateUserRequest>> batches = ListUtil.split(requests, 100);
        batches.forEach(this::processBatch);
    }
    
    private void processBatch(List<UpdateUserRequest> batch) {
        // 批处理逻辑
        batch.forEach(request -> {
            // 处理单个请求
        });
    }
}
```

### 3. Bean工具类使用
```java
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;

@Service
public class UserServiceImpl implements UserService {
    
    public UserResponse updateUser(Long id, UpdateUserRequest request) {
        User user = userRepository.findById(id)
            .orElseThrow(() -> new UserNotFoundException("用户不存在: " + id));
        
        // 使用BeanUtil进行属性拷贝，忽略null值
        BeanUtil.copyProperties(request, user, CopyOptions.create()
            .setIgnoreNullValue(true)
            .setIgnoreError(true));
        
        user.setUpdatedAt(LocalDateTime.now());
        
        return UserResponse.fromEntity(userRepository.save(user));
    }
    
    public List<UserResponse> convertUsers(List<User> users) {
        if (CollUtil.isEmpty(users)) {
            return Collections.emptyList();
        }
        
        return users.stream()
            .map(user -> BeanUtil.copyProperties(user, UserResponse.class))
            .collect(Collectors.toList());
    }
}
```

### 4. 日期工具类使用
```java
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;

@Service
public class UserServiceImpl implements UserService {
    
    public List<UserResponse> getUsersCreatedInRange(String startDate, String endDate) {
        // 字符串转LocalDateTime
        LocalDateTime start = StrUtil.isBlank(startDate) ? 
            LocalDateTimeUtil.beginOfDay(LocalDateTime.now().minusDays(30)) :
            LocalDateTimeUtil.parse(startDate);
            
        LocalDateTime end = StrUtil.isBlank(endDate) ?
            LocalDateTimeUtil.endOfDay(LocalDateTime.now()) :
            LocalDateTimeUtil.parse(endDate);
        
        return userRepository.findByCreatedAtBetween(start, end)
            .stream()
            .map(UserResponse::fromEntity)
            .collect(Collectors.toList());
    }
    
    public String formatUserCreatedTime(User user) {
        return LocalDateTimeUtil.format(user.getCreatedAt(), "yyyy-MM-dd HH:mm:ss");
    }
}
```

### 5. 数字和金额工具类使用
```java
import cn.hutool.core.util.NumberUtil;

@Service
public class OrderServiceImpl implements OrderService {
    
    public OrderResponse calculateOrder(OrderRequest request) {
        // 金额计算，保留2位小数
        BigDecimal unitPrice = NumberUtil.toBigDecimal(request.unitPrice());
        BigDecimal quantity = NumberUtil.toBigDecimal(request.quantity());
        BigDecimal totalAmount = NumberUtil.mul(unitPrice, quantity);
        
        // 折扣计算
        if (NumberUtil.isGreater(request.discount(), BigDecimal.ZERO)) {
            totalAmount = NumberUtil.sub(totalAmount, request.discount());
        }
        
        // 确保金额不为负数
        totalAmount = NumberUtil.max(totalAmount, BigDecimal.ZERO);
        
        return OrderResponse.builder()
            .totalAmount(NumberUtil.round(totalAmount, 2))
            .build();
    }
}
```

---

## 📖 阿里巴巴开发手册补充规范

### 1. 命名规范
```java
// ✅ 正确命名
public class UserServiceImpl implements UserService {
    private static final int MAX_RETRY_COUNT = 3;
    private static final String DEFAULT_USER_STATUS = "ACTIVE";
    
    private final UserRepository userRepository;
    private final EmailService emailService;
    
    public UserResponse createUser(CreateUserRequest request) {
        // 局部变量使用驼峰命名
        String processedEmail = request.email().toLowerCase();
        LocalDateTime currentTime = LocalDateTime.now();
        
        return UserResponse.fromEntity(savedUser);
    }
}

// ❌ 错误命名
public class userservice {  // 类名应该大驼峰
    private static final int max_retry_count = 3;  // 常量应该全大写
    private String UserName;  // 字段应该小驼峰
    
    public UserResponse CreateUser(CreateUserRequest request) {  // 方法应该小驼峰
        String Processed_Email = request.email();  // 变量应该小驼峰
    }
}
```


```

---

## ⚙️ 技术栈使用标准

### 核心技术栈
- **Spring Boot 3.4.x + JPA + PostgreSQL + Redis**
- **依赖注入**：构造函数注入，避免字段注入
- **数据库操作**：JPA Repository + Specification
- **响应格式**：统一使用 `ApiResponse<T>`
- **工具类**：优先使用Hutool工具包
- **时间处理**：使用LocalDateTime系列
- **判空处理**：使用Optional和Hutool工具类

### 关键使用模式
```java
// 构造函数注入模式
@Service
public class UserService {
    private final UserRepository userRepository;
    
    public UserService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }
}

// Optional判空模式
return userRepository.findById(id)
    .map(UserResponse::fromEntity)
    .orElseThrow(() -> new BusinessException("用户不存在: " + id));

// Hutool工具类模式
if (StrUtil.isBlank(request.name())) {
    throw new BusinessException("用户名不能为空");
}

// LocalDateTime时间模式
user.setCreatedAt(LocalDateTime.now());
user.setExpireTime(LocalDateTime.now().plusDays(30));

// Stream API模式
return users.stream()
    .filter(User::isActive)
    .map(UserResponse::fromEntity)
    .collect(Collectors.toList());
```

---

## 📋 开发流程检查清单

### 接口开发检查清单
- [ ] **依赖注入**：使用构造函数注入，避免字段注入
- [ ] **Controller层**：只做HTTP交互，不包含业务逻辑
- [ ] **Service层**：接口与实现分离，包含核心业务逻辑
- [ ] **Repository层**：继承JpaRepository，复杂查询使用Specification
- [ ] **参数校验**：使用jakarta.validation注解
- [ ] **异常处理**：使用@RestControllerAdvice全局处理
- [ ] **Java8+特性**：使用LocalDateTime、Optional、Stream API
- [ ] **Hutool工具类**：使用StrUtil、CollUtil等替代原生判断
- [ ] **编码规范**：遵循阿里巴巴开发手册

### 代码质量检查清单
- [ ] 是否使用构造函数注入？
- [ ] 是否使用LocalDateTime处理时间？
- [ ] 是否使用Optional处理可能为null的值？
- [ ] 是否使用Hutool工具类进行字符串和集合操作？
- [ ] 是否使用Stream API进行集合处理？
- [ ] 是否遵循RESTful API设计规范？
- [ ] 是否使用了统一的异常处理？
- [ ] 是否避免了N+1查询问题？
- [ ] 是否遵循了分层架构原则？
- [ ] 是否使用了合适的日志级别？

### 代码审查要点
- [ ] 命名是否清晰明确？
- [ ] 是否有不必要的中间变量？
- [ ] 是否正确使用了事务注解？
- [ ] 是否有合适的注释说明？
- [ ] 是否遵循了常量定义规范？
- [ ] 是否正确处理了异常情况？
- [ ] 是否使用了合适的数据类型？
- [ ] 是否由重复代码需要提取到公共方法中?

---

## 🎯 总结

这套Java开发规范确保了：
1. **现代化开发**：充分利用Java8+新特性和Hutool工具类
2. **架构清晰**：四层架构明确职责分离，构造函数依赖注入
3. **代码质量**：遵循阿里巴巴开发手册，统一编码规范
4. **异常处理**：全局异常处理，统一错误响应格式
5. **性能优化**：避免N+1查询，合理使用缓存和批量操作
6. **可维护性**：清晰的命名规范，合适的注释说明

**严格遵循这些规范，将确保Java项目的高质量交付和长期可维护性！**

---

*本指南基于Spring Boot 3.4.x、Java8+新特性、Hutool工具包和阿里巴巴开发手册制定，是现代Java开发的最佳实践总结。*