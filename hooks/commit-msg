#!/bin/sh

COMMIT_MSG_MAX_LENGTH=1024
COMMIT_FORMAT_POLICY_TAG="[COMMIT_FORMAT_POLICY] ERROR::"

# Match the format of a commit message using regular expression
COMMIT_FORMAT_REGEX="^\[[A-z0-9\s&]+\] #(((BEB)-[0-9]+)|(N/A)) (chore|docs|feat|fix|refactor|style|test)\: .+$"

message_file="$1"

# Read the contents of the commit message file
message=$(cat "$message_file" | tr -d '\r')

if [ "$(echo "$message" | grep -E 'This reverts commit')" != "" ] ||
   [ "$(echo "$message" | grep -E 'It looks like you may be committing a cherry-pick')" != "" ] ||
   [ "$(echo "$message" | grep -E '^Merge branch')" != "" ]
then
  exit 0
fi

if [ "${#message}" -gt "$COMMIT_MSG_MAX_LENGTH" ]; then
  echo "${COMMIT_FORMAT_POLICY_TAG} Commit message length ${#message} exceeds the maximum allowed length: ${COMMIT_MSG_MAX_LENGTH}.\n${COMMIT_ERROR_BANNER}"
  exit 1
fi

if ! echo "$message" | grep -Eq "$COMMIT_FORMAT_REGEX"; then
  echo "${COMMIT_ERROR_BANNER}\n${COMMIT_FORMAT_POLICY_TAG} Commit message format invalid. Please follow the format as follows:\n[Contributor name] #jira_issue_code chore|docs|feat|fix|refactor|style|test: your_commit_message.\nE.g.\n[Name] #BEB-1234 fix: error message not displaying correctly.\nOr:\n[Name] #N/A fix: publish the correct version of accessory import template.\nVerb list:\n  feat     - new feature for the USER, not a new feature for build script\n  fix      - bug fix for the USER, not a fix to a build script\n  refactor - refactoring production code e.g. renaming a variable\n  test     - adding missing tests, refactoring tests, no production code change\n  chore    - updating gradle version etc., no production code change\n  style    - CODE style (not front-end style), code formatting, missing semicolon etc.,\n             no production code change\n  docs     - changes to the documentation, comment etc.\nReference: https://gist.github.com/joshbuchea/6f47e86d2510bce28f8e7f42ae84c716\n${COMMIT_ERROR_BANNER}"
  exit 1
fi

exit 0