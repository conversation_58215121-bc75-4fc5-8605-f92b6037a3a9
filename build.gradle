plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.7'
    id 'io.spring.dependency-management' version '1.1.7'
    id 'pmd'
    id 'jacoco'
}

group = 'com.mb.bebest'
version = '0.0.1'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    maven { url 'https://maven.aliyun.com/repository/central' }
    maven { url 'https://maven.aliyun.com/repository/jcenter' }
    maven { url 'https://maven.aliyun.com/repository/google' }
    maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
    mavenCentral()
    maven { url 'https://repo.spring.io/milestone' }
}

ext {
    set('springCloudVersion', "2024.0.2")
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'org.springframework.cloud:spring-cloud-starter-gateway'
    implementation 'org.springframework.cloud:spring-cloud-starter-loadbalancer'
    implementation 'org.springframework.cloud:spring-cloud-starter-circuitbreaker-reactor-resilience4j'
    implementation 'org.apache.commons:commons-pool2'
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    annotationProcessor 'org.projectlombok:lombok'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    implementation 'io.zonky.test:embedded-postgres:2.1.0'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
    implementation 'org.springdoc:springdoc-openapi-starter-webflux-ui:2.8.9'
    implementation 'org.postgresql:postgresql:42.7.5'
    implementation 'commons-io:commons-io:2.14.0'
    implementation'com.fasterxml.jackson.core:jackson-databind:2.18.2'
    implementation 'commons-lang:commons-lang:20030203.000129'
    implementation 'org.apache.commons:commons-text:1.10.0'
    implementation 'com.alibaba.fastjson2:fastjson2:2.0.57'
    implementation 'io.jsonwebtoken:jjwt-api:0.12.6'
    implementation 'io.jsonwebtoken:jjwt-impl:0.12.6'
    implementation 'io.jsonwebtoken:jjwt-jackson:0.12.6'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        mavenBom "org.testcontainers:testcontainers-bom:1.19.0"
    }
}


pmd {
    toolVersion = '6.41.0'
    ignoreFailures = false
    consoleOutput = true
    ruleSetFiles = rootProject.files('pmd/pmd-ruleSets.xml')
}

tasks.withType(Pmd) {
    include '**/*.java'
}

task installGitHooks(type: Copy) {
    from file("${rootProject.projectDir}/hooks/pre-push")
    into {
        file("${rootProject.projectDir}/.git/hooks")
    }
    fileMode 0755
    from file("${rootProject.projectDir}/hooks/commit-msg")
    into {
        file("${rootProject.projectDir}/.git/hooks")
    }
    fileMode 0755
}

test {
    useJUnitPlatform()
    testLogging {
        events "passed", "skipped", "failed"
        exceptionFormat "full"
        showStandardStreams = false
    }
    systemProperty 'junit.jupiter.execution.timeout.default', '300s'
    systemProperty 'junit.jupiter.execution.timeout.testable.method.default', '300s'
}

jacocoTestCoverageVerification {
    violationRules {
        rule {
            element = 'PACKAGE'
            includes = ['com.mb.bebest.*.controller',
                        'com.mb.bebest.*.service'
            ]
            limit {
                counter = 'BRANCH'
                minimum = 0.00
            }
            limit {
                counter = 'LINE'
                minimum = 0.00
            }
            limit {
                counter = 'METHOD'
                minimum = 0.00
            }
            limit {
                counter = 'CLASS'
                minimum = 0.00
            }
        }
    }
}

jacocoTestReport {
    afterEvaluate {
        classDirectories.setFrom(files(classDirectories.files.collect {
            fileTree(dir: it, includes: ['**/controller/**',
                                         '**/service/**'
            ])
        }))
    }
    reports {
        xml.required = true
        html.required = true
    }
}

jacoco {
    toolVersion = "0.8.10"
}

tasks.withType(Test) {
    finalizedBy jacocoTestReport
}

springBoot {
    buildInfo()
}

tasks.withType(JavaExec) {
    jvmArgs = [
        '-Xms512m',
        '-Xmx2g',
        '-XX:+UseG1GC',
        '-XX:G1HeapRegionSize=16m',
        '-XX:+UseStringDeduplication',
        '-XX:+OptimizeStringConcat'
    ]
}

compileJava.dependsOn installGitHooks
