<?xml version="1.0" encoding="UTF-8"?>
<ruleset name="PMD Ruleset"
         xmlns="http://pmd.sourceforge.net/ruleset/2.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0 http://pmd.sourceforge.net/ruleset_2_0_0.xsd">
    <description>PMD Ruleset</description>

    <exclude-pattern>.*/build/generated-test-sources/.*|.*/src/test/.*</exclude-pattern>


    <rule ref="category/java/design.xml/ExcessiveMethodLength">
        <properties>
            <property name="minimum" value="40"/>
        </properties>
    </rule>

    <rule ref="category/java/design.xml/ExcessiveClassLength">
        <properties>
            <property name="minimum" value="500"/>
        </properties>
    </rule>

    <rule ref="category/java/design.xml/CognitiveComplexity">
        <properties>
            <property name="reportLevel" value="20" />
        </properties>
    </rule>

    <rule ref="category/java/bestpractices.xml">
        <exclude name="AbstractClassWithoutAbstractMethod"/>
        <exclude name="GuardLogStatement"/>
        <exclude name="ArrayIsStoredDirectly"/>
    </rule>
    <rule ref="category/java/codestyle.xml">
        <exclude name="LongVariable"/>
        <exclude name="ShortVariable"/>
        <exclude name="LocalVariableCouldBeFinal"/>
        <exclude name="MethodArgumentCouldBeFinal"/>
        <exclude name="ClassNamingConventions"/>
        <exclude name="OnlyOneReturn"/>
        <exclude name="CommentDefaultAccessModifier"/>
        <exclude name="DefaultPackage"/>
        <exclude name="GenericsNaming"/>
        <exclude name="AtLeastOneConstructor"/>
        <exclude name="TooManyStaticImports"/>
    </rule>
    <rule ref="category/java/design.xml">
        <exclude name="LawOfDemeter"/>
        <exclude name="TooManyFields"/>
        <exclude name="TooManyMethods"/>
        <exclude name="AbstractClassWithoutAnyMethod"/>
        <exclude name="SignatureDeclareThrowsException"/>
        <exclude name="AvoidThrowingRawExceptionTypes"/>
        <exclude name="AvoidCatchingGenericException"/>
        <exclude name="ExcessiveImports"/>
        <exclude name="UseObjectForClearerAPI"/>
        <exclude name="LoosePackageCoupling"/>
        <exclude name="UseUtilityClass"/>
    </rule>
    <rule ref="category/java/documentation.xml">
        <exclude name="CommentRequired"/>
        <exclude name="CommentContent"/>
        <exclude name="CommentSize"/>
        <exclude name="UncommentedEmptyConstructor"/>
        <exclude name="UncommentedEmptyMethodBody"/>
    </rule>
    <rule ref="category/java/errorprone.xml">
        <exclude name="BeanMembersShouldSerialize"/>
        <exclude name="NullAssignment"/>
        <exclude name="DataflowAnomalyAnalysis"/>
        <exclude name="AvoidDuplicateLiterals"/>
    </rule>
    <rule ref="category/java/multithreading.xml">
        <exclude name="UseConcurrentHashMap"/>
    </rule>
    <rule ref="category/java/performance.xml">
        <exclude name="AvoidInstantiatingObjectsInLoops"/>
    </rule>
</ruleset>
