# AI助手系统接口文档

## 1. 问题相似性搜索接口

### 1.1 搜索相似问题
**接口地址：** `POST /api/issue/ai-assistant/search`

**请求参数：**
```json
{
  "query": "问题描述文本",
  "dataSources": ["<PERSON><PERSON>", "<PERSON>", "Starc", "RKS", "Zeus"],
  "dataTypes": ["AI_Job_Retrieval", "Real_Time_Retrieval"],
  "filters": {
    "platform": "All platform",
    "carline": "All Models",
    "status": "All Status",
    "dateRange": {
      "start": "2023-01-01",
      "end": "2024-12-31"
    }
  },
  "page": 1,
  "pageSize": 10
}
```

**响应参数：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 3,
    "results": [
      {
        "issueId": "RDVV-1082",
        "title": "Battery performance degradation at -15°C",
        "description_summary": "Vehicle shows significant reduction in battery range...",
        "source": "Finas",
        "date": "2023-11-22",
        "carline": "V294",
        "platform": "254",
        "status": "Resolved",
        "similarity": "92%",
        "sourceIssueId": "RDVV-1082-SRC"
      }
    ]
  }
}
```

### 1.2 获取问题详情
**接口地址：** `GET /api/issue/ai-assistant/issue/{issueId}`

**请求参数：**
- issueId: 问题ID

**响应参数：**

**基础信息（所有数据源共同字段）：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "issueId": "RDVV-1082",
    "title": "Battery performance degradation at -15°C",
    "description_summary": "详细问题描述摘要...",
    "source": "Zeus",
    "date": "2023-11-22",
    "carline": "V294",
    "platform": "254",
    "status": "Resolved",
    "sourceIssueId": "RDVV-1082-SRC",
    "detailData": {
      // 根据数据源返回不同的详情结构
    }
  }
}
```

**Zeus数据源详情结构：**
```json
"detailData": {
  "iraId": 12345,
  "eightdId": 67890,
  "complaintDescription": "客户投诉描述...",
  "damageCodes": ["DC001", "DC002"],
  "iraResponsible": {
    "account": "john.doe",
    "name": "John Doe",
    "department": "Quality Department"
  },
  "trafficLights": {
    "production": "Red",
    "aftersales": "Yellow"
  },
  "moduleGroup": "MG21",
  "issueProject": "V206",
  "iraTitle": "问题IRA标题描述",
  "steps": {
    "s3": {
      "production": {
        "text": "生产S3步骤描述",
        "dueDate": "2025-07-21",
        "finishedOn": "2025-07-21T10:30:00Z"
      },
      "aftersales": {
        "text": "售后S3步骤描述",
        "dueDate": "2025-07-25",
        "finishedOn": null
      }
    },
    "s4": {
      "text": "S4步骤描述",
      "dueDate": "2025-08-01",
      "completeOn": "2025-08-01T15:45:00Z"
    },
    "s5": {
      "production": {
        "text": "生产S5步骤描述",
        "dueDate": "2025-08-10",
        "finishedOn": null
      },
      "aftersales": {
        "text": "售后S5步骤描述",
        "dueDate": "2025-08-15",
        "finishedOn": null
      }
    },
    "s6": {
      "production": {
        "text": "生产S6步骤描述",
        "dueDate": "2025-08-20",
        "finishedOn": null
      },
      "aftersales": {
        "text": "售后S6步骤描述",
        "dueDate": "2025-08-25",
        "finishedOn": null
      }
    },
    "s7": {
      "text": "S7步骤描述",
      "dueDate": "2025-09-01",
      "completeOn": null
    },
    "s8": {
      "text": "S8步骤描述",
      "dueDate": "2025-09-15",
      "completeOn": null
    }
  }
}
```

**Starc数据源详情结构：**
```json
"detailData": {
  "priority": "High",
  "severity": "Critical",
  "submittedAt": "2023-11-22T09:00:00Z",
  "modifiedAt": "2023-11-23T14:30:00Z",
  "detectedOnDate": "2023-11-20T16:00:00Z",
  "lastStatusChange": "2023-11-23T14:30:00Z",
  "submitter": {
    "name": "Jane Smith",
    "email": "<EMAIL>"
  },
  "detectedBy": "Test Team A",
  "productInfo": {
    "model": "V294",
    "system": "Battery Management",
    "device": "BMS Controller",
    "domain": "Energy Management",
    "market": "European Market",
    "affectedMarkets": "Europe, North America",
    "defectArea": "Software"
  },
  "testInfo": {
    "environment": "Test Lab Environment 1",
    "setup": "测试配置详细信息...",
    "event": "Cold Weather Battery Test",
    "actualTestEvent": "Extreme Cold Test (-20°C)",
    "testGroup": "Battery Testing Group"
  },
  "technicalInfo": {
    "detectedInEStand": "E-Stand 2.3",
    "detectedInEeRelease": "EE Release 1.5",
    "occurrence": "Always",
    "triggerInfo": "Temperature below -15°C for more than 10 minutes"
  },
  "analysis": {
    "closingReason": "Design Fix Implemented",
    "summary": "Battery management algorithm updated to handle extreme cold conditions",
    "preAnalysisOwner": "Technical Analysis Team",
    "rootCause": "Insufficient battery conditioning algorithm for extreme temperatures",
    "solutionDescription": "Updated BMS firmware with enhanced cold weather algorithms"
  },
  "platforms": ["254", "235"]
}
```

**Finas数据源详情结构：**
```json
"detailData": {
  "complainId": "COMP-2023-001",
  "objectId": "OBJ-V294-001",
  "lastUpdateTime": "2023-11-23T10:30:00Z",
  "vehicleNumber": "VIN123456789",
  "complaintDate": "2023-11-22T08:00:00Z",
  "reporter": {
    "name": "Customer Service Rep",
    "userId": "CSR001"
  },
  "responsible": {
    "userId": "QM001",
    "name": "Quality Manager"
  },
  "vehicleInfo": {
    "mileage": 15000.50,
    "mileageUnits": "km",
    "seriesModulesVehicle": "V294-Battery",
    "seriesModulesPartsMain": "Battery Pack",
    "seriesModulesParts": "BMS",
    "seriesModulesSub": "Temperature Sensor"
  },
  "objectInfo": {
    "objectName": "Battery Management System",
    "partNumber": "BMS-V294-001",
    "maturityScore": "8",
    "damageClass": "2",
    "reliabilityRelevantClassification": "A"
  },
  "complaint": {
    "status": "2",
    "text": "Battery performance significantly reduced in cold weather...",
    "cause": "Temperature sensor malfunction during extreme cold conditions"
  },
  "sslInfo": {
    "code": "SSL001",
    "codeText": "Battery System Failure",
    "type": "HW",
    "typeText": "Hardware Related"
  },
  "measures": [
    {
      "title": "Immediate Response Measure",
      "description": "Replace temperature sensor and update BMS calibration"
    },
    {
      "title": "Long-term Solution",
      "description": "Design improvement for enhanced cold weather performance"
    }
  ]
}
```

**Duke数据源详情结构：**
```json
"detailData": {
  "topicId": 12345,
  "project": "V294 Development",
  "moduleGroup": "Battery Management System",
  "topicTitle": "Cold Weather Battery Performance",
  "topicDescription": "Investigation into battery performance degradation in extreme cold conditions",
  "topicCause": "Insufficient battery management algorithms for cold weather operation",
  "dates": {
    "dueDate": "2025-08-01T00:00:00Z",
    "createdDate": "2023-11-20T09:00:00Z",
    "modifiedDate": "2023-11-23T14:30:00Z"
  },
  "status": "In Progress",
  "maturity": 3,
  "issueCode": "DUKE-2023-001",
  "issueEditor": {
    "firstName": "John",
    "lastName": "Doe",
    "department": "Quality Engineering",
    "userId": "jdoe001"
  },
  "issueResponsible": {
    "firstName": "Jane",
    "lastName": "Smith",
    "department": "Battery Engineering",
    "userId": "jsmith001"
  },
  "priority": {
    "level": "High",
    "daysToDue": 45,
    "daysSinceCreated": 3,
    "daysSinceModified": 0,
    "isOverdue": false,
    "isCritical": true
  },
  "measures": [
    {
      "id": 1,
      "description": "Update battery management algorithms",
      "dueDate": "2025-07-15T00:00:00Z",
      "type": "Software Update",
      "status": "In Progress",
      "feedback": "Algorithm development 70% complete",
      "responsible": {
        "firstName": "Mike",
        "lastName": "Johnson",
        "department": "Software Engineering",
        "userId": "mjohnson001"
      },
      "daysToDue": 30,
      "daysSinceCreated": 10,
      "isOverdue": false
    }
  ],
  "testObjects": [
    "Test Vehicle 001",
    "Test Vehicle 002",
    "Bench Test Setup"
  ],
  "starcReferences": [
    {
      "starcId": 98765,
      "status": "Open"
    }
  ],
  "vaws": {
    "project": "V294 Development",
    "subProject": "Battery System",
    "moduleGroup": "BMS Integration",
    "details": [
      {
        "vaw": "Cold Weather Battery Test",
        "maturity": 4,
        "rubrics": "Temperature performance validation",
        "category": "System Test",
        "complexityLevel": "High"
      }
    ]
  }
}
```

## 2. 搜索历史管理接口

### 2.1 获取搜索历史
**接口地址：** `GET /api/issue/ai-assistant/search-history`

**请求参数：**
- userId: 用户ID
- timeRange: today/yesterday/7days/15days

**响应参数：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "today": [
      {
        "id": 1,
        "query": "The issue description I searched today---1",
        "searchTime": "2024-01-15 10:30:00"
      }
    ],
    "yesterday": [
      {
        "id": 2,
        "query": "The issue description I searched yesterday---1",
        "searchTime": "2024-01-14 15:20:00"
      }
    ]
  }
}
```

### 2.2 删除搜索历史
**接口地址：** `DELETE /api/issue/ai-assistant/search-history/{historyId}`

**请求参数：**
- historyId: 历史记录ID

**响应参数：**
```json
{
  "code": 200,
  "message": "删除成功"
}
```

### 2.3 清空搜索历史
**接口地址：** `DELETE /api/issue/ai-assistant/search-history/clear`

**请求参数：**
```json
{
  "userId": "用户ID",
  "timeRange": "today/yesterday/7days/15days/all"
}
```

## 3. 配置选项接口

### 3.1 获取数据源列表
**接口地址：** `GET /api/issue/ai-assistant/data-sources`

**响应参数：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "value": "Finas",
      "label": "Finas"
    },
    {
      "value": "Duke",
      "label": "Duke"
    },
    {
      "value": "Starc",
      "label": "Starc"
    },
    {
      "value": "RKS",
      "label": "RKS"
    },
    {
      "value": "Zeus",
      "label": "Zeus"
    }
  ]
}
```

### 3.2 获取数据类型列表
**接口地址：** `GET /api/issue/ai-assistant/data-types`

**响应参数：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "value": "AI_Job_Retrieval",
      "label": "AI任务检索"
    },
    {
      "value": "Real_Time_Retrieval",
      "label": "实时检索"
    }
  ]
}
```

## 4. 过滤选项接口

### 4.1 获取平台列表
**接口地址：** `GET /api/issue/ai-assistant/platforms`

**响应参数：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "value": "All platform",
      "label": "All platform"
    },
    {
      "value": "254",
      "label": "254"
    },
    {
      "value": "206",
      "label": "206"
    },
    {
      "value": "235",
      "label": "235"
    }
  ]
}
```

### 4.2 获取车型列表
**接口地址：** `GET /api/issue/ai-assistant/carlines`

**响应参数：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "value": "All Models",
      "label": "All Models"
    },
    {
      "value": "V294",
      "label": "V294"
    },
    {
      "value": "V235",
      "label": "V235"
    },
    {
      "value": "V223",
      "label": "V223"
    },
    {
      "value": "V254",
      "label": "V254"
    },
    {
      "value": "V206",
      "label": "V206"
    }
  ]
}
```

### 4.3 获取状态列表
**接口地址：** `GET /api/issue/ai-assistant/statuses`

**响应参数：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "value": "All Status",
      "label": "All Status"
    },
    {
      "value": "Open",
      "label": "Open"
    },
    {
      "value": "In Progress",
      "label": "In Progress"
    },
    {
      "value": "Resolved",
      "label": "Resolved"
    },
    {
      "value": "Closed",
      "label": "Closed"
    }
  ]
}
```

## 5. AI问题报告接口

### 5.1 获取AI问题报告列表
**接口地址：** `GET /api/issue/ai-assistant/ai-issue-report`

**请求参数：**
- dateFrom: 开始日期 (YYYY-MM-DD，可选)
- dateTo: 结束日期 (YYYY-MM-DD，可选)
- dataSources: 数据源筛选 (数组，可选: ["Finas", "Duke", "Starc", "RKS", "Zeus"])
- platform: 平台筛选 (可选: "All platform", "254", "206", "235")
- carline: 车型筛选 (可选: "All Models", "V294", "V235", "V223", "V254", "V206")
- status: 状态筛选 (可选: "All Status", "Open", "Closed", "In progress")
- page: 页码 (默认1)
- pageSize: 每页大小 (默认10)

**响应参数：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "issues": [
      {
        "issueId": "Issue ID 1",
        "title": "Issue Title 1",
        "date": "2025-07-06",
        "source": "Finas",
        "hasAiReport": true
      },
      {
        "issueId": "Issue ID 2",
        "title": "Issue Title 2",
        "date": "2025-07-06",
        "source": "Starc",
        "hasAiReport": true
      },
      {
        "issueId": "Issue ID 3",
        "title": "Issue Title 3",
        "date": "2025-07-06",
        "source": "Duke",
        "hasAiReport": true
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "pageSize": 10,
      "total": 47
    }
  }
}
```

### 5.2 获取AI问题智能报告详情
**接口地址：** `GET /api/issue/ai-assistant/ai-issue-report/{issueId}`

**请求参数：**
- issueId: 问题ID

**响应参数：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "reportTitle": "Intelligent Issue Report",
    "targetIssue": {
      "issueId": "Issue ID 1",
      "title": "Issue Title 1",
      "description": "Question: Issue ID: title"
    },
    "similarIssues": [
      {
        "similarityScore": "90%",
        "description": "Similar issue with V294 historical issue, similar issue description, same failure code (RTC). Details see Bebest.",
        "details": "Issue recorded from Finas system shows similarity with previous issues documented in V294 testing phase. Common failure patterns identified in high-temperature conditions.",
        "source": "Finas",
        "date": "2023-08-15",
        "carline": "V294",
        "status": "Close"
      },
      {
        "similarityScore": "85%",
        "description": "Similar issue with V235 historical issue, due to similar issue description. Details see Bebest.",
        "details": "Duke recorded issue shows partial match with V235 test findings. Similar symptoms but potentially different root causes.",
        "source": "Duke",
        "date": "2023-05-21",
        "carline": "V235",
        "status": "Close"
      },
      {
        "similarityScore": "70%",
        "description": "Similar issue with V235 historical issue, due to similar issue description. Details see Bebest.",
        "details": "Duke recorded issue shows partial match with V235 test findings. Similar symptoms but potentially different root causes.",
        "source": "Starc",
        "date": "2023-05-12",
        "carline": "V235",
        "status": "Close"
      }
    ]
  }
}
```

### 5.3 获取问题详细信息（根据数据源变化）
**接口地址：** `GET /api/issue/ai-assistant/issue-detail/{issueId}`

**请求参数：**
- issueId: 问题ID

**响应参数：**

**Starc数据源响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "dataSource": "Starc",
    "issueId": "17254490",
    "title": "[TLA] ADAS: Traffic light view or augmented reality is steadily zooming in and out",
    "status": "Closed",
    "lastStatusChange": "2025-07-07 09:54",
    "actualTestEvent": "GEN20X.I3_STAR3.0_BAUREIHEN-E/E-INTENSIVERPROBUNG_ECE_R14.0",
    "severity": "VoCA 2 / IBN High",
    "priority": "Low",
    "model": "C167 MOPF2",
    "market": "ECE",
    "system": "Traffic Light Assist",
    "platform": "Gen20xi3",
    "domain": "Telematics",
    "device": "CIVIC_RSU_Gen20x.i3",
    "defectArea": "RSU_Vehicle_Functions",
    "preAnalysisOwner": "Automated Driving",
    "submittedBy": "Aws Mohammed [MOHAMAW] 2025-07-01T15:31:57",
    "testGroup": "Test Group - ADAS GEN6 Vehicle Testing CHN",
    "detectedBy": "Aws Mohammed [MOHAMAW]",
    "detectedOnDate": "2025-07-07 12:04",
    "testEnvironment": "223-4599",
    "testEvent": "Baureihen-E/E-Intensiverprobung",
    "testSetup": "CIVIC: E229.1-929, RSU: E230.0-470769",
    "detectedInEStand": "E230.0",
    "closingReason": "No Defect",
    "detectedInEeRelease": "R14.0_EE-Loop_KW25",
    "rootCause": "not a bug",
    "affectedMarket": "ECE",
    "occurrence": "Always",
    "description": "PRECONDITIONS: Driving in city with navigation active. DTR on. Augmented reality is ON and Traffic Light View is ON. ERROR PATH/ACTION: Approaching red traffic light. Vehicle standstill. MISBEHAVIOR/REACTION: Traffic light view or augmented reality is steadily zooming in and out. Maybe view is switching permanently between augmented reality and traffic lght view. EXPECTED BEHAVIOR: Steady and stable view. ERROR RECOVERY: None. SPEC REFERENCE: Vehicle:167-05783 Other Attachments:video-20250624-075251-7c721872.mov, Participants: Joachim Jung, Gerd Hickel Time of Logger: 2025-06-24 09:52 BP: 223 HU: 52 FU: Tool Used: TDS 1.12.0",
    "analysisSummary": "Last mode for focus ob driver is not specified in i3 SOP but for i2",
    "solutionDescription": "Duplicate of the masterticket [ISSUE:16486610]",
    "trigger": "R14.0_BP:223 HU:52"
  }
}
```

**Finas数据源响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "dataSource": "Finas",
    "complainId": "COMP-001",
    "title": "Battery thermal management issue",
    "vehicleNumber": "VIN123456789",
    "complaintDate": "2023-08-15",
    "reportingPersonName": "John Smith",
    "responsibleUserName": "Quality Manager",
    "mileage": 15000,
    "seriesModulesVehicle": "V294-Battery",
    "partNumber": "BMS-V294-001",
    "complainText": "Customer reports battery performance issues in hot weather conditions...",
    "sslCode": "SSL001",
    "sslType": "Hardware"
  }
}
```

**Duke数据源响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "dataSource": "Duke",
    "topicId": 12345,
    "project": "V235 Development",
    "moduleGroup": "Battery Management System",
    "topicTitle": "Battery performance optimization",
    "dueDate": "2023-06-01",
    "status": "Closed",
    "maturity": 5,
    "issueCode": "DUKE-001",
    "priorityLevel": "High",
    "isCritical": true
  }
}
```