<knowledge>
## 项目《Java团队开发与测试规范完整指南》核心约束

### 构造函数依赖注入（强制规范）
```java
// ✅ 正确 - 构造函数注入
@RequiredArgsConstructor
public class OAuth2Controller {
    private final OAuth2Service oAuth2Service;
}

// ❌ 错误 - 字段注入（严禁）
@Service
public class UserServiceImpl implements UserService {
    @Autowired
    private UserRepository userRepository; // 违反规范
}
```

### 私有化常量定义规范
```java
// ✅ 正确
private static final String USER_STATUS_ACTIVE = "active";
private static final List<String> ALLOWED_ROLES = List.of("admin", "user", "guest");
private static final int DEFAULT_TIMEOUT = 30;
private static final BigDecimal MAX_AMOUNT = new BigDecimal("999999.99");

// ❌ 错误
public static final String USER_STATUS_ACTIVE = "active"; // 不应该public
String USER_STATUS_ACTIVE = "active"; // 缺少static final
```

### Stream API优先使用规范
```java
// ✅ 正确 - 使用Stream API
List<String> activeUserEmails = users.stream()
    .filter(User::isActive)
    .map(User::getEmail)
    .filter(email -> email != null && !email.isEmpty())
    .distinct()
    .sorted()
    .collect(Collectors.toList());

// ❌ 错误 - 传统循环（简单场景禁用）
List<Long> userIds = new ArrayList<>();
for (User user : users) {
    if (user.isActive()) {
        userIds.add(user.getId());
    }
}
```

### 链式调用强制规范
```java
// ✅ 正确 - 链式调用
StringBuilder sb = new StringBuilder()
    .append("Hello")
    .append(" ")
    .append("World")
    .append("!")
    .reverse();

User user = User.builder()
    .name(request.getName())
    .email(request.getEmail())
    .status(UserStatus.ACTIVE)
    .createdAt(LocalDateTime.now())
    .build();
```

### N+1查询禁止规范
```java
// ✅ 正确 - JOIN FETCH
@Query("SELECT u FROM User u JOIN FETCH u.role WHERE u.status = :status")
List<User> findActiveUsersWithRoles(@Param("status") UserStatus status);

// ✅ 正确 - 批量查询
List<Long> userIds = users.stream().map(User::getId).collect(Collectors.toList());
Map<Long, Role> roleMap = roleRepository.findByUserIdIn(userIds)
    .stream()
    .collect(Collectors.toMap(Role::getUserId, Function.identity()));

// ❌ 错误 - N+1查询（严禁）
List<User> users = userRepository.findByStatus("active");
for (User user : users) {
    Role role = roleRepository.findByUserId(user.getId()); // N+1查询
}
```

### LocalDateTime时间处理强制规范
```java
// ✅ 正确 - 使用LocalDateTime
@Entity
public class User {
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "birth_date")
    private LocalDate birthDate;
}

// Service中的时间处理
public UserResponse createUser(CreateUserRequest request) {
    User user = User.builder()
        .name(request.name())
        .email(request.email())
        .createdAt(LocalDateTime.now())
        .build();
    
    LocalDateTime expireTime = LocalDateTime.now().plusDays(30);
    LocalDate nextMonth = LocalDate.now().plusMonths(1);
    
    return UserResponse.fromEntity(userRepository.save(user));
}

// ❌ 错误 - 使用Date（完全禁用）
private Date createdAt;
private Date updatedAt;
```

### Hutool工具类强制使用规范
```java
// ✅ 正确 - 使用Hutool工具类
if (StrUtil.isBlank(request.name())) {
    throw new IllegalArgumentException("用户名不能为空");
}

if (CollUtil.isEmpty(userIds)) {
    return Collections.emptyList();
}

if (ObjectUtil.isNull(claim.getSupplierFirstReplyDate())) {
    claim.setSupplierFirstReplyDate(LocalDate.now());
}

// ❌ 错误 - 使用原生判断（禁用）
if (request.name() == null || request.name().trim().isEmpty()) {
    throw new IllegalArgumentException("用户名不能为空");
}

if (keyword == null || "".equals(keyword.trim())) {
    return Collections.emptyList();
}
```

### 异常处理规范
```java
// ✅ 正确 - BusinessException + ExceptionEnum
public class BusinessException extends RuntimeException {
    @Getter
    private int code;
    @Getter
    private String message;
    @Getter
    private Object errorData;

    public BusinessException(ExceptionEnum exceptionEnum) {
        this.code = exceptionEnum.getCode();
        this.message = exceptionEnum.getMessage();
    }
}

public enum ExceptionEnum {
    USERNAME_OR_PASSWORD_IS_NULL(001, "用户名或密码不能为空！"),
    USERNAME_NOT_EXIST(004, "登录用户: %s 不存在！"),
    CAPTCHA_IS_ERROR(013, "验证码错误！");
    
    @Getter
    private Integer code;
    @Getter
    private String message;
}

// ✅ 正确 - 异常处理
try {
    userService.createUser(request);
} catch (DuplicateUserException e) {
    log.warn("用户已存在: {}", request.getEmail(), e);
    throw new BusinessException("用户邮箱已被注册");
} catch (Exception e) {
    log.error("创建用户失败: {}", request, e);
    throw new SystemException("系统异常，请稍后重试");
}

// ❌ 错误 - 空catch块（严禁）
try {
    userService.createUser(request);
} catch (Exception e) {
    // 什么都不做 - 违反规范
}
```

### Group Validation分组校验规范
```java
@Data
public class AddAndEditVehicleRequest {
    public interface Edit{}
    public interface Add{}

    @NotBlank(message = "VehicleId cannot be empty!", groups = {Edit.class})
    private String id;

    @NotBlank(message = "PlateNumber cannot be empty!", groups = {Add.class, Edit.class})
    @Pattern(regexp = "^[\\u4e00-\\u9fa5]{1}[A-Z]{1}[A-Z_0-9]{5,6}$",
            message = "The license plate number does not meet the specifications",
            groups = {Add.class, Edit.class})
    private String plateNumber;

    @NotNull(message = "IsDefault cannot be empty!", groups = {Add.class, Edit.class})
    private Boolean isDefault;
}

// Controller中使用
@PostMapping
public ApiResponse<VehicleResponse> addVehicle(
        @Validated(AddAndEditVehicleRequest.Add.class) @RequestBody AddAndEditVehicleRequest request) {
    return ApiResponse.success(vehicleService.addVehicle(request));
}

@PutMapping("/{id}")
public ApiResponse<VehicleResponse> editVehicle(
        @PathVariable String id,
        @Validated(AddAndEditVehicleRequest.Edit.class) @RequestBody AddAndEditVehicleRequest request) {
    return ApiResponse.success(vehicleService.editVehicle(id, request));
}
```

### 现代Switch语法使用规范
```java
// ✅ 正确 - 现代Switch语法
case SUPPLIER -> {
    handleSupplierEdits(claim, requestDto);
    claim.setSupplierLatestReplyDate(LocalDate.now());
    if (ObjectUtil.isNull(claim.getSupplierFirstReplyDate())) {
        claim.setSupplierFirstReplyDate(LocalDate.now());
    }
    switch (requestDto.getSupplierFeedback()) {
        case SUPPLIER_FEEDBACK_ACCEPT -> claim.setProcessStatus(ProcessStatusEnum.CLAIM_APPROVED.name());
        case SUPPLIER_FEEDBACK_REFUSE -> claim.setProcessStatus(ProcessStatusEnum.CLAIM_REJECTED.name());
        case SUPPLIER_FEEDBACK_INSUFFICIENT_MATERIALS ->
                claim.setProcessStatus(ProcessStatusEnum.MATERIALS_REQUIRED.name());
        default -> {
        }
    }
}
default -> {
}

// ❌ 避免 - 多个else if
if (type == SUPPLIER) {
    // 处理逻辑
} else if (type == CUSTOMER) {
    // 处理逻辑
} else if (type == ADMIN) {
    // 处理逻辑
}
```

### 项目特定约束
- **禁止单元测试**：项目规定不生成对应的单元测试代码
- **DRY原则强制**：发现重复代码必须提取到公共方法
- **拒绝无意义注释**：避免只解释代码字面意思的"废话注释"
- **Lombok注解优先**：可以用lombok注解的地方就使用lombok，减少样板代码
</knowledge>
