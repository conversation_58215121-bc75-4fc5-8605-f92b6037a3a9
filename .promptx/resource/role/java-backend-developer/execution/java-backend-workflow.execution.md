<execution>
  <constraint>
    ## 项目技术栈约束（严格遵循）
    - **Spring Boot版本**：Spring Boot 3.4.x（项目指定版本）
    - **数据库技术栈**：JPA + PostgreSQL + Redis（项目标准技术栈）
    - **Java版本**：Java 8+新特性强制使用（LocalDateTime、Optional、Stream API）
    - **工具类库**：Hutool工具包强制使用，替代原生判断
    - **代码规范**：严格遵循《Java团队开发与测试规范完整指南》

    ## 开发约束（零容忍违反）
    - **依赖注入**：必须使用@RequiredArgsConstructor + final字段，严禁@Autowired字段注入
    - **四层架构**：Controller-DTO-Service-Repository严格分层，禁止跨层调用
    - **时间处理**：强制使用LocalDateTime系列，完全禁用Date类型
    - **异常处理**：使用BusinessException + ExceptionEnum + @RestControllerAdvice
    - **单元测试**：项目规定不生成单元测试代码
  </constraint>

  <rule>
    ## 代码编写强制规则（零容忍违反）
    - **构造函数依赖注入**：必须使用@RequiredArgsConstructor + final字段，严禁@Autowired字段注入
    - **私有化常量**：类级别常量使用private static final，命名全大写下划线
    - **命名清晰性**：所有变量、方法、类名必须"望文生义"，避免缩写
    - **禁止反射滥用**：99%属性访问通过getter/setter，严格限制反射使用
    - **避免中间变量**：避免仅使用一次的中间变量，直接返回表达式结果
    - **Stream API优先**：集合操作优先使用Stream API，避免传统for循环
    - **链式调用强制**：检查顺序操作，可以链式调用的必须进行链式调用
    - **禁止N+1查询**：严禁循环内数据库查询，使用JOIN FETCH或批量查询
    - **常量类管理**：将常量定义到专门常量类，不允许魔法值
    - **异常处理规范**：不允许空catch块，必须记录日志或重新抛出
    - **DRY原则**：发现重复代码必须提取到公共方法
    - **拒绝无意义注释**：避免只解释代码字面意思的"废话注释"
    - **Lombok注解使用**：可以用lombok的地方必须使用，减少样板代码
    - **Switch语法使用**：避免多个else if，使用现代switch case语句

    ## 四层架构强制规则（不可违反）
    - **Controller层职责**：仅HTTP交互、参数校验、调用Service、响应封装
    - **DTO层职责**：定义请求/响应数据结构，与Entity严格分离
    - **Service层职责**：核心业务逻辑、事务管理、接口与实现分离
    - **Repository层职责**：数据持久化操作，继承JpaRepository + Specification

    ## RESTful API设计规则
    - **资源命名**：使用复数名词，如/api/users、/api/orders
    - **HTTP方法**：GET查询、POST创建、PUT完全更新、PATCH部分更新、DELETE删除
    - **参数校验**：使用jakarta.validation注解，支持group validation
    - **全局异常处理**：使用@RestControllerAdvice，优先BusinessException
  </rule>

  <guideline>
    ## Java8+新特性使用指导
    - **LocalDateTime时间处理**：优先使用LocalDateTime、LocalDate、LocalTime
    - **Optional判空处理**：使用Optional处理可能为null的值，配合Hutool工具类
    - **Stream API集合处理**：集合操作优先使用Stream API，提高代码可读性
    - **函数式接口使用**：使用Function、Predicate、Consumer等函数式接口

    ## Hutool工具类使用指导
    - **字符串工具类**：使用StrUtil替代原生字符串判断，避免== null
    - **集合工具类**：使用CollUtil进行集合操作，如isEmpty、distinct等
    - **Bean工具类**：使用BeanUtil进行属性拷贝，支持忽略null值
    - **日期工具类**：使用LocalDateTimeUtil进行时间格式化和解析
    - **数字工具类**：使用NumberUtil进行金额计算，保留精度

    ## 代码质量指导原则
    - **规范优先**：严格遵循项目开发规范，对违规代码零容忍
    - **性能意识**：关注数据库查询效率、内存使用和并发安全
    - **可读性**：清晰的命名、合理的注释、简洁的逻辑
    - **可维护性**：模块化设计、低耦合高内聚、易于扩展
    - **持续改进**：主动发现重构机会，提取公共方法
  </guideline>

  <process>
    ## 项目规范开发流程

    ### 1. 接口开发标准流程
    ```mermaid
    flowchart TD
        A[需求分析] --> B[API设计]
        B --> C[DTO设计]
        C --> D[Entity设计]
        D --> E[Controller实现]
        E --> F[Service实现]
        F --> G[Repository实现]
        G --> H[功能测试]
        H --> I[规范检查]
        I --> J[代码提交]
    ```

    ### 2. 代码规范检查流程
    ```mermaid
    flowchart TD
        A[代码完成] --> B{依赖注入检查}
        B -->|违规| C[修正为构造函数注入]
        B -->|合规| D{时间处理检查}
        D -->|违规| E[修正为LocalDateTime]
        D -->|合规| F{判空处理检查}
        F -->|违规| G[修正为Hutool工具类]
        F -->|合规| H{N+1查询检查}
        H -->|违规| I[修正为批量查询]
        H -->|合规| J[规范检查通过]
        C --> B
        E --> D
        G --> F
        I --> H
    ```

    ### 3. 四层架构实现流程
    ```mermaid
    flowchart TD
        A[Controller层] --> A1[HTTP交互]
        A1 --> A2[参数校验]
        A2 --> A3[调用Service]
        A3 --> A4[封装ApiResponse]

        B[DTO层] --> B1[请求DTO设计]
        B1 --> B2[响应DTO设计]
        B2 --> B3[与Entity分离]

        C[Service层] --> C1[接口定义]
        C1 --> C2[业务逻辑实现]
        C2 --> C3[事务管理]

        D[Repository层] --> D1[继承JpaRepository]
        D1 --> D2[Specification复杂查询]
        D2 --> D3[避免N+1查询]
    ```

    ### 4. 性能优化检查流程
    ```mermaid
    flowchart TD
        A[性能检查] --> B{数据库查询}
        B -->|N+1查询| C[使用JOIN FETCH]
        B -->|正常| D{集合操作}
        D -->|传统循环| E[改为Stream API]
        D -->|正常| F{对象创建}
        F -->|不必要中间变量| G[直接返回表达式]
        F -->|正常| H[性能检查通过]
        C --> B
        E --> D
        G --> F
    ```
  </process>

  <criteria>
    ## 项目规范遵循标准（零容忍）

    ### 强制性规范检查清单
    - [ ] **依赖注入**：使用@RequiredArgsConstructor + final字段，避免@Autowired字段注入
    - [ ] **Controller层**：只做HTTP交互，不包含业务逻辑
    - [ ] **Service层**：接口与实现分离，包含核心业务逻辑
    - [ ] **Repository层**：继承JpaRepository，复杂查询使用Specification
    - [ ] **参数校验**：使用jakarta.validation注解，支持group validation
    - [ ] **异常处理**：使用BusinessException + ExceptionEnum + @RestControllerAdvice
    - [ ] **Java8+特性**：使用LocalDateTime、Optional、Stream API
    - [ ] **Hutool工具类**：使用StrUtil、CollUtil等替代原生判断
    - [ ] **编码规范**：遵循项目开发规范，命名清晰、常量私有化

    ### 代码质量检查清单
    - [ ] 是否使用构造函数注入？
    - [ ] 是否使用LocalDateTime处理时间？
    - [ ] 是否使用Optional处理可能为null的值？
    - [ ] 是否使用Hutool工具类进行字符串和集合操作？
    - [ ] 是否使用Stream API进行集合处理？
    - [ ] 是否遵循RESTful API设计规范？
    - [ ] 是否使用了统一的异常处理？
    - [ ] 是否避免了N+1查询问题？
    - [ ] 是否遵循了分层架构原则？
    - [ ] 是否有重复代码需要提取到公共方法？

    ### 代码审查要点
    - [ ] 命名是否清晰明确？
    - [ ] 是否有不必要的中间变量？
    - [ ] 是否正确使用了事务注解？
    - [ ] 是否有合适的注释说明？
    - [ ] 是否遵循了常量定义规范？
    - [ ] 是否正确处理了异常情况？
    - [ ] 是否使用了合适的数据类型？
    - [ ] 是否可以进行链式调用优化？

    ### 性能质量标准
    - **数据库查询**：避免N+1查询，使用JOIN FETCH或批量查询
    - **集合操作**：优先使用Stream API，避免传统for循环
    - **对象创建**：避免不必要的中间变量，直接返回表达式结果
    - **缓存使用**：合理使用Redis缓存，提高查询效率

    ### 项目特定标准
    - **禁止单元测试**：按项目规定，不生成单元测试代码
    - **Switch语法**：避免多个else if，使用现代switch case语句
    - **Lombok使用**：可以用lombok的地方必须使用，减少样板代码
    - **DRY原则**：发现重复代码必须提取到公共方法
  </criteria>
</execution>
