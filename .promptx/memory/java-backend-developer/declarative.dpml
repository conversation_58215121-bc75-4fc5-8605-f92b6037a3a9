<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1754371783412_a77jbre1d" time="2025/08/05 13:29">
    <content>
      重要的接口安全优化实践：
    
      用户要求修改deleteSearchHistory接口，我严格按照项目规范进行了以下优化：
    
      1. **权限控制强化**：
      - 在Service层获取当前用户ID：UserContextUtil.getCurrentUserId()
      - 验证删除权限：只能删除自己的搜索历史记录
      - 防止越权访问：if (!currentUserId.equals(searchHistory.getUserId()))
    
      2. **异常处理规范化**：
      - 替换RuntimeException为BusinessException + ExceptionEnum
      - 新增异常枚举：SEARCH_HISTORY_NOT_FOUND、ACCESS_DENIED
      - 使用Optional.orElseThrow()优雅处理查询结果
    
      3. **日志记录完善**：
      - 记录操作前的用户ID和记录ID
      - 记录操作成功的确认信息
      - 便于问题排查和审计
    
      4. **代码规范遵循**：
      - 用户ID在Service层获取，不在Controller层
      - 使用BusinessException统一异常处理
      - 遵循项目的四层架构设计原则
    
      这次优化体现了接口安全设计的重要性：权限验证、异常处理、日志记录缺一不可。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754372110316_iewm0mtdh" time="2025/08/05 13:35">
    <content>
      重要的接口重构实践：
    
      用户要求重构searchSimilarIssues接口，指出当前代码很多不符合规范。我严格按照项目规范进行了全面重构：
    
      1. **架构设计优化**：
      - 用户ID在Service层获取：UserContextUtil.getCurrentUserId()
      - 方法职责单一：拆分为validateSearchRequest、callAiApi、saveSearchDataAndBuildResponse等专门方法
      - 异常处理规范：使用BusinessException + ExceptionEnum替代RuntimeException
    
      2. **参数校验强化**：
      - 查询内容非空校验：StrUtil.isBlank(request.getQuery())
      - 分页大小限制：pageSize不能超过100
      - 业务规则校验：确保请求参数合法性
    
      3. **异常处理完善**：
      - 新增异常枚举：QUERY_IS_NULL、PAGE_SIZE_TOO_LARGE、AI_API_CALL_FAILED、SAVE_SEARCH_DATA_FAILED
      - 分层异常处理：参数校验异常、API调用异常、数据保存异常
      - 详细日志记录：便于问题排查和监控
    
      4. **Mock设计合理**：
      - AI API暂时Mock实现，便于后续替换
      - 保持接口设计的前瞻性和扩展性
      - TODO标记清晰，便于后续开发
    
      这次重构体现了企业级Java开发的核心原则：规范性、安全性、可维护性、可测试性。
    </content>
    <tags>#最佳实践</tags>
  </item>
  <item id="mem_1754372245787_m5hcdc5j1" time="2025/08/05 13:37">
    <content>
      重要的参数校验原则纠正：
    
      用户指出我在searchSimilarIssues接口中添加validateSearchRequest方法是多余的，因为DTO中已经有完整的参数校验注解。这是一个重要的设计原则纠正：
    
      1. **避免重复校验**：
      - DTO层已有@NotBlank、@Max等校验注解
      - Controller层通过@Valid @RequestBody触发校验
      - GlobalExceptionHandler处理MethodArgumentNotValidException
      - 不应该在Service层重复相同的校验逻辑
    
      2. **Spring Boot校验机制**：
      - DTO注解校验：处理基础格式和业务规则校验
      - Service层校验：只处理复杂的业务逻辑校验（如数据库唯一性）
      - 全局异常处理：统一处理校验异常并返回标准格式
    
      3. **DRY原则**：
      - Don&#x27;t Repeat Yourself - 不要重复自己
      - 同样的校验规则不要在多个地方实现
      - 充分利用Spring Boot的注解校验机制
    
      4. **正确的分层职责**：
      - Controller层：HTTP处理 + 触发校验(@Valid)
      - DTO层：定义校验规则(@NotBlank、@Max等)
      - Service层：业务逻辑 + 复杂业务校验
      - 全局异常处理：统一校验异常处理
    
      这个纠正让我更深刻理解了Spring Boot参数校验的正确使用方式和分层职责。
    </content>
    <tags>#最佳实践</tags>
  </item>
</memory>