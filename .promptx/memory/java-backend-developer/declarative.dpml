<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1754028689436_75ta9iseg" time="2025/08/01 14:11">
    <content>
      Java团队开发完整指南 - 核心规范要点：
    
      1. 强制性规范：
      - 构造函数依赖注入（@RequiredArgsConstructor + final）
      - 私有化常量（private static final）
      - 命名清晰性（望文生义）
      - 禁止反射滥用
      - 避免不必要中间变量
      - Stream API优先
      - 链式调用与Builder模式
      - 禁止N+1查询
      - 常量类管理
      - 异常处理规范
      - 不允许写单元测试
      - DRY原则
      - 拒绝无意义注释
      - Lombok注解使用
      - switch语法使用
    
      2. 四层架构：Controller-DTO-Service-Repository，职责分离
    
      3. RESTful API设计：资源命名复数、HTTP方法规范、参数校验、全局异常处理
    
      4. Java8+特性：LocalDateTime时间处理、Optional判空、Stream API、函数式接口
    
      5. Hutool工具类：StrUtil字符串、CollUtil集合、BeanUtil属性拷贝、DateUtil日期、NumberUtil数字
    
      6. 技术栈：Spring Boot 3.4.x + JPA + PostgreSQL + Redis
    
      7. 开发流程检查清单：依赖注入、分层架构、参数校验、异常处理、Java8+特性、Hutool工具类
    </content>
    <tags>#流程管理 #工具使用</tags>
  </item>
  <item id="mem_1754029100369_4tho6kxea" time="2025/08/01 14:18">
    <content>
      BeBest Gateway Service 架构分析：
    
      1. 核心功能：
      - API网关：Spring Cloud Gateway路由转发
      - OAuth2认证：集成Mercedes-Benz SSO
      - JWT Token管理：生成、验证、缓存、注销
      - 全局过滤器：统一认证和用户信息传递
      - 熔断降级：Resilience4j Circuit Breaker
    
      2. 技术栈：
      - Spring Boot 3.4.7 + WebFlux (响应式)
      - Spring Cloud Gateway 2024.0.2
      - JWT (jjwt 0.12.6)
      - Redis缓存 + 内存缓存
      - PostgreSQL + JPA
      - Resilience4j熔断器
    
      3. 路由配置：
      - /api/user/** → user-service (9082)
      - /api/issue/** → issue-service (9083)
      - /api/ai/** → ragflow-service (9084)
    
      4. 认证流程：
      OAuth2授权码模式 → JWT Token生成 → Redis缓存 → 请求头传递用户信息
    
      5. 关键组件：
      - JwtAuthenticationFilter：全局认证过滤器
      - OAuth2Controller：认证接口
      - TokenService：Token管理
      - OAuth2Service：OAuth2集成
    </content>
    <tags>#流程管理</tags>
  </item>
  <item id="mem_1754298700293_0mc3yb92j" time="2025/08/04 17:11">
    <content>
      用户在bebest-issue-service项目中完成了以下开发工作：
    
      1. **SQL脚本修复**：修复了Script-60.sql中全文索引的NULL值处理问题，将原来的字符串连接改为使用COALESCE函数处理NULL值
    
      2. **Entity实体类创建**：基于SQL脚本创建了三个实体类
      - AiSummaryIssue.java：AI问题摘要实体，包含所有数据库字段映射，使用@UpdateTimestamp自动更新时间
      - SearchHistory.java：搜索历史实体，使用JSONB存储搜索参数
      - SearchHistoryIssue.java：关联表实体，保留外键ID字段但移除JPA关联注解
    
      3. **Repository DAO层创建**：创建了三个简洁的Repository接口
      - AiSummaryIssueRepository
      - SearchHistoryRepository
      - SearchHistoryIssueRepository
      所有Repository都继承JpaRepository和JpaSpecificationExecutor，暂时不包含自定义方法
    
      4. **设计原则**：
      - 严格遵循项目Java开发规范
      - 移除JPA关联注解，在业务逻辑中手动处理关联关系
      - 保留外键ID字段用于业务层关联查询
      - 使用LocalDateTime替代Date
      - 使用Lombok注解减少样板代码
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754299806769_8si6ltk2v" time="2025/08/04 17:30">
    <content>
      用户在bebest-issue-service项目中进行了重要更新：
    
      1. **依赖管理更新**：
      - 在build.gradle中添加了Hutool依赖：implementation &#x27;cn.hutool:hutool-all:5.8.20&#x27;
      - 现在可以使用Hutool工具类进行字符串、集合、日期等操作
    
      2. **项目结构调整**：
      - ApiResponse类从dto.response包移动到common包下
      - 创建了完整的异常处理体系在common包下：
      - BusinessException.java：自定义业务异常类
      - ExceptionEnum.java：异常枚举定义
      - GlobalExceptionHandler.java：全局异常处理器，使用@RestControllerAdvice
    
      3. **异常处理规范**：
      - 使用BusinessException + ExceptionEnum模式
      - GlobalExceptionHandler处理各种异常类型
      - 支持参数化异常消息和错误数据
      - 使用Hutool的ArrayUtil进行数组操作
    
      4. **AI助手模块完整架构**：
      - Controller层：AiAssistantController处理/api/issue/ai-assistant/search接口
      - Service层：AiAssistantService接口和AiAssistantServiceImpl实现
      - DTO层：AiAssistantSearchRequest和AiAssistantSearchResponse
      - 统一响应：使用common.ApiResponse包装所有API响应
    
      所有代码严格遵循项目Java开发规范，使用构造函数注入、Lombok注解、LocalDateTime等。
    </content>
    <tags>#工具使用</tags>
  </item>
  <item id="mem_1754300104506_xs7sc3xd3" time="2025/08/04 17:35">
    <content>
      重要的Controller设计原则更新：
    
      用户明确要求在Controller中直接返回自定义的ApiResponse，不需要使用ResponseEntity包装。
    
      正确的Controller方法签名应该是：
      ```java
      @PostMapping(&quot;/search&quot;)
      public ApiResponse&lt;AiAssistantSearchResponse&gt; searchSimilarIssues(@Valid @RequestBody AiAssistantSearchRequest request) {
      AiAssistantSearchResponse response = aiAssistantService.searchSimilarIssues(request);
      return ApiResponse.success(response);
      }
      ```
    
      而不是：
      ```java
      public ResponseEntity&lt;ApiResponse&lt;AiAssistantSearchResponse&gt;&gt; searchSimilarIssues(...)
      ```
    
      这样的设计更简洁，避免了双重包装，直接使用自定义的ApiResponse作为统一响应格式。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754300197201_6j36eshk4" time="2025/08/04 17:36">
    <content>
      重要的代码编写规范强化：
    
      用户强调严格遵守项目规范中&quot;避免不必要的中间变量&quot;原则。
    
      ❌ 错误写法（违反规范）：
      ```java
      public ApiResponse&lt;AiAssistantSearchResponse&gt; searchSimilarIssues(@Valid @RequestBody AiAssistantSearchRequest request) {
      AiAssistantSearchResponse response = aiAssistantService.searchSimilarIssues(request);
      return ApiResponse.success(response);
      }
      ```
    
      ✅ 正确写法（遵循规范）：
      ```java
      public ApiResponse&lt;AiAssistantSearchResponse&gt; searchSimilarIssues(@Valid @RequestBody AiAssistantSearchRequest request) {
      return ApiResponse.success(aiAssistantService.searchSimilarIssues(request));
      }
      ```
    
      核心原则：
      1. 避免仅使用一次的中间变量
      2. 直接返回表达式结果
      3. 保持代码简洁性
      4. 严格遵循项目Java开发规范
    
      这是项目规范的强制要求，必须严格执行。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754300291320_e1u75at4c" time="2025/08/04 17:38">
    <content>
      重要的Controller参数校验规范：
    
      用户强调Controller类必须添加@Validated注解来启用Spring框架的参数校验功能。
    
      ✅ 正确的Controller类注解配置：
      ```java
      @Tag(name = &quot;AI助手&quot;, description = &quot;AI助手相关接口&quot;)
      @RestController
      @RequestMapping(&quot;/api/issue/ai-assistant&quot;)
      @RequiredArgsConstructor
      @Validated  // 必须添加此注解启用参数校验
      public class AiAssistantController {
    
      // 方法参数使用@Valid进行对象校验
      public ApiResponse&lt;AiAssistantSearchResponse&gt; searchSimilarIssues(@Valid @RequestBody AiAssistantSearchRequest request) {
      return ApiResponse.success(aiAssistantService.searchSimilarIssues(request));
      }
      }
      ```
    
      核心要点：
      1. Controller类上必须添加@Validated注解
      2. 这样启用Spring的参数校验框架
      3. 后续如果需要校验单个字段（如@PathVariable、@RequestParam），就不需要再写额外注解
      4. 对象参数仍然使用@Valid注解进行校验
      5. 这是Spring Boot参数校验的标准配置
    
      这是Controller层的强制规范要求。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754301333900_g8d2mnmx4" time="2025/08/04 17:55">
    <content>
      用户更新了Flyway配置信息：
    
      用户在bebest-issue-service项目中修改了Flyway的配置，具体的配置更改内容需要在后续开发中参考用户的实际配置。
    
      重要要点：
      1. 用户已经添加了Flyway依赖到build.gradle中：
      - implementation &#x27;org.flywaydb:flyway-core&#x27;
      - implementation &#x27;org.flywaydb:flyway-database-postgresql&#x27;
    
      2. 用户在application-local.yml中配置了Flyway相关设置
    
      3. 用户创建了数据库迁移文件：src/main/resources/db/migration/V1__Create_initial_tables.sql
    
      4. 配置原则：
      - application.yml中不包含明文配置信息，使用环境变量
      - application-local.yml中包含本地开发环境的具体配置
      - Flyway用于数据库版本管理和迁移
    
      5. 用户对Flyway配置进行了自定义调整，需要按照用户的实际配置为准。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754301432955_xm5uxfvd7" time="2025/08/04 17:57">
    <content>
      用户删除了AI生成的V1迁移脚本，使用自己编写的SQL脚本：
    
      重要更新：
      1. 删除了src/main/resources/db/migration/V1__Create_initial_tables.sql文件
      2. 用户已经准备了自己的SQL迁移脚本
      3. 用户的SQL脚本是基于之前讨论的Script-60.sql（已修复NULL值处理问题）
      4. 用户的脚本包含三个表：ai_summary_issues、search_history、search_history_issue
      5. 用户的脚本已经包含了正确的全文索引配置（使用COALESCE处理NULL值）
    
      开发原则：
      - 优先使用用户提供的SQL脚本而不是AI生成的脚本
      - 用户对数据库结构有明确的设计要求
      - 遵循用户的数据库迁移管理方式
      - Flyway迁移文件应该使用用户编写和验证过的SQL脚本
    
      今后涉及数据库迁移时，应该参考用户的实际SQL脚本内容和结构。
    </content>
    <tags>#其他</tags>
  </item>
</memory>