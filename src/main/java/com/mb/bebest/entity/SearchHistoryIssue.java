package com.mb.bebest.entity;

import jakarta.persistence.*;
import lombok.*;

/**
 * 搜索历史与问题关联实体类
 * 对应数据库表：search_history_issue
 */
@Entity
@Table(name = "search_history_issue", indexes = {
    @Index(name = "idx_search_issue", columnList = "searchHistoryId, issueId")
})
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class SearchHistoryIssue {

    /**
     * 关联ID（主键）
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 搜索历史ID
     */
    @Column(name = "search_history_id", nullable = false)
    private Long searchHistoryId;

    /**
     * 问题ID
     */
    @Column(name = "ai_summary_issue_id", nullable = false)
    private Long aiSummaryIssueId;


}
