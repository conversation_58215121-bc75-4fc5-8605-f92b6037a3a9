package com.mb.bebest.entity;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * AI问题摘要实体类
 * 对应数据库表：ai_summary_issues
 */
@Entity
@Table(name = "ai_summary_issues", indexes = {
    @Index(name = "idx_ai_summary_issues_id", columnList = "issueId"),
    @Index(name = "idx_ai_summary_issues_data_source", columnList = "dataSource"),
    @Index(name = "idx_ai_summary_issues_platform_carline", columnList = "platform, carline"),
    @Index(name = "idx_ai_summary_issues_status", columnList = "status"),
    @Index(name = "idx_ai_summary_issues_issue_date", columnList = "issueDate"),
    @Index(name = "idx_ai_summary_issues_source_issue", columnList = "sourceIssueId")
})
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class AiSummaryIssue {

    /**
     * 问题ID（主键）
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 问题编号（如RDVV-1082）
     */
    @Column(name = "issue_id", length = 50, nullable = false, unique = true)
    private String issueId;

    /**
     * 问题标题
     */
    @Column(name = "title", length = 500, nullable = false)
    private String title;

    /**
     * 问题描述摘要
     */
    @Column(name = "description_summary", columnDefinition = "TEXT")
    private String descriptionSummary;

    /**
     * 客户投诉内容
     */
    @Column(name = "customer_complaint", columnDefinition = "TEXT")
    private String customerComplaint;

    /**
     * 数据源（Finas, Duke, Starc, RKS, Zeus）
     */
    @Column(name = "data_source", length = 50, nullable = false)
    private String dataSource;

    /**
     * 平台（254, 206, 235等）
     */
    @Column(name = "platform", length = 50)
    private String platform;

    /**
     * 车型（V294, V235, V223等）
     */
    @Column(name = "carline", length = 50)
    private String carline;

    /**
     * 状态（Open, In Progress, Resolved, Closed）
     */
    @Column(name = "status", length = 50)
    private String status;

    /**
     * 问题类型
     */
    @Column(name = "issue_type", length = 50)
    private String issueType;

    /**
     * 模块组
     */
    @Column(name = "module_group", length = 50)
    private String moduleGroup;

    /**
     * 项目
     */
    @Column(name = "project", length = 50)
    private String project;

    /**
     * 损坏位置
     */
    @Column(name = "damage_location", length = 100)
    private String damageLocation;

    /**
     * 损坏类型
     */
    @Column(name = "damage_type", length = 50)
    private String damageType;

    /**
     * 损坏代码
     */
    @Column(name = "damage_code", length = 50)
    private String damageCode;

    /**
     * 负责人
     */
    @Column(name = "responsible_person", length = 100)
    private String responsiblePerson;

    /**
     * 问题日期
     */
    @Column(name = "issue_date")
    private LocalDate issueDate;

    /**
     * 相似度评分
     */
    @Column(name = "similarity_score", precision = 5, scale = 2)
    private BigDecimal similarityScore;

    /**
     * 排序
     */
    @Column(name = "rank_order")
    private Integer rankOrder;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 源问题ID
     */
    @Column(name = "source_issue_id", length = 50)
    private String sourceIssueId;
}
