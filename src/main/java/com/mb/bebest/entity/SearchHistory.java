package com.mb.bebest.entity;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 搜索历史实体类
 * 对应数据库表：search_history
 */
@Entity
@Table(name = "search_history", indexes = {
    @Index(name = "idx_user_time", columnList = "userId, searchTime"),
    @Index(name = "idx_search_time", columnList = "searchTime")
})
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class SearchHistory {

    /**
     * 历史记录ID（主键）
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 搜索关键词
     */
    @Column(name = "query", nullable = false, columnDefinition = "TEXT")
    private String query;

    /**
     * 搜索参数（包含过滤条件）
     * 使用JSONB类型存储复杂的搜索参数
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "search_params", columnDefinition = "jsonb")
    private String searchParams;

    /**
     * 搜索结果数量
     */
    @Column(name = "result_count")
    @Builder.Default
    private Integer resultCount = 0;

    /**
     * 搜索时间
     */
    @CreationTimestamp
    @Column(name = "search_time", nullable = false, updatable = false)
    private LocalDateTime searchTime;


}
