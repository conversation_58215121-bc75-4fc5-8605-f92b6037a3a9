package com.mb.bebest.repository;

import com.mb.bebest.entity.SearchHistoryIssue;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 搜索历史与问题关联Repository
 * 提供基础CRUD操作
 */
@Repository
public interface SearchHistoryIssueRepository extends JpaRepository<SearchHistoryIssue, Long>, JpaSpecificationExecutor<SearchHistoryIssue> {

    /**
     * Find association records by search history ID
     * @param searchHistoryId search history ID
     * @return list of SearchHistoryIssue
     */
    List<SearchHistoryIssue> findBySearchHistoryId(Long searchHistoryId);

    /**
     * Delete association records by search history ID
     * @param searchHistoryId search history ID
     */
    @Modifying
    @Query("DELETE FROM SearchHistoryIssue shi WHERE shi.searchHistoryId = :searchHistoryId")
    void deleteBySearchHistoryId(@Param("searchHistoryId") Long searchHistoryId);
}
