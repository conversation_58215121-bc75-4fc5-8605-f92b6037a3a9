package com.mb.bebest.repository;

import com.mb.bebest.entity.SearchHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 搜索历史Repository
 * 提供基础CRUD操作
 */
@Repository
public interface SearchHistoryRepository extends JpaRepository<SearchHistory, Long>, JpaSpecificationExecutor<SearchHistory> {

    /**
     * 根据用户ID和时间范围查找搜索历史（按时间倒序）
     */
    List<SearchHistory> findByUserIdAndSearchTimeBetweenOrderBySearchTimeDesc(Long userId, LocalDateTime startTime, LocalDateTime endTime);


}
