package com.mb.bebest.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class TokenCacheService {

    private final ConcurrentHashMap<String, String> storage = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Long> expiry = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Set<String>> setStorage = new ConcurrentHashMap<>();

    public boolean hasKey(String key) {
        if (isExpired(key)) {
            storage.remove(key);
            expiry.remove(key);
            return false;
        }
        return storage.containsKey(key);
    }

    public void delete(String key) {
        storage.remove(key);
        expiry.remove(key);
        setStorage.remove(key);
    }


    public void set(String key, String value, long timeout, TimeUnit unit) {
        storage.put(key, value);
        expiry.put(key, System.currentTimeMillis() + unit.toMillis(timeout));
    }

    public String get(String key) {
        if (isExpired(key)) {
            storage.remove(key);
            expiry.remove(key);
            return null;
        }
        return storage.get(key);
    }


    // Set操作
    public void addToSet(String key, String... values) {
        setStorage.computeIfAbsent(key, k -> ConcurrentHashMap.newKeySet()).addAll(java.util.Arrays.asList(values));
    }

    public void removeFromSet(String key, String... values) {
        Set<String> set = setStorage.get(key);
        if (set != null) {
            for (String value : values) {
                set.remove(value);
            }
        }
    }

    public Set<String> getSetMembers(String key) {
        return setStorage.getOrDefault(key, java.util.Collections.emptySet());
    }

    private boolean isExpired(String key) {
        Long expiryTime = expiry.get(key);
        return expiryTime != null && expiryTime < System.currentTimeMillis();
    }
} 