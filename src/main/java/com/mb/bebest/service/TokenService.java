package com.mb.bebest.service;

import com.alibaba.fastjson2.JSON;
import com.mb.bebest.config.JwtProperties;
import com.mb.bebest.dto.TokenResponse;
import com.mb.bebest.dto.UserInfo;
import com.mb.bebest.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@SuppressWarnings("ALL")
@RequiredArgsConstructor
public class TokenService {

    private final JwtUtil jwtUtil;

    private final JwtProperties jwtProperties;

    @Autowired
    private TokenCacheService tokenCacheService;

    private static final String TOKEN_PREFIX = "bebest:token:";
    private static final String USER_TOKEN_PREFIX = "bebest:user:tokens:";

    /**
     * 生成并缓存token
     */
    public TokenResponse generateAndCacheToken(UserInfo userInfo) {
        // 生成token ID和session ID
        String tokenId = UUID.randomUUID().toString();
        String sessionId = UUID.randomUUID().toString();

        // 准备claims
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userInfo.getUserId());
        claims.put("email", userInfo.getEmail());
        claims.put("name", userInfo.getName());
        claims.put("tokenId", tokenId);
        claims.put("sessionId", sessionId);

        // 生成access token和refresh token
        String accessToken = jwtUtil.generateToken(userInfo.getUsername(), claims);

        // 缓存token信息
        cacheToken(tokenId, userInfo);
        addUserToken(userInfo.getUsername(), tokenId, sessionId);

        log.info("Generated and cached token for user: {}", userInfo.getUsername());

        return new TokenResponse(
                accessToken,
                jwtProperties.getExpiration() / 1000,
                userInfo
        );
    }

    /**
     * 验证token
     */
    public boolean validateToken(String token) {
        try {
            String username = jwtUtil.getUsernameFromToken(token);
            if (username == null) {
                return false;
            }

            // 验证token格式和过期时间
            if (!jwtUtil.validateToken(token, username)) {
                return false;
            }

            // 从token中获取tokenId
            Map<String, Object> tokenInfo = jwtUtil.parseToken(token);
            String tokenId = (String) tokenInfo.get("tokenId");

            // 检查缓存中是否存在该token
            String cacheKey = TOKEN_PREFIX + tokenId;
            return tokenCacheService.hasKey(cacheKey);

        } catch (Exception e) {
            log.error("Token validation failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取token中的用户信息
     */
    public UserInfo getUserInfoFromToken(String token) {
        try {
            Map<String, Object> tokenInfo = jwtUtil.parseToken(token);
            if (tokenInfo == null) {
                return null;
            }

            String tokenId = (String) tokenInfo.get("tokenId");
            String cacheKey = TOKEN_PREFIX + tokenId;
            String userInfoJson = tokenCacheService.get(cacheKey);

            if (userInfoJson != null) {
                return JSON.parseObject(userInfoJson, UserInfo.class);
            }

            return null;
        } catch (Exception e) {
            log.error("Failed to get user info from token: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 注销token
     */
    public void revokeToken(String token) {
        try {
            Map<String, Object> tokenInfo = jwtUtil.parseToken(token);
            if (tokenInfo != null) {
                String tokenId = (String) tokenInfo.get("tokenId");
                String username = (String) tokenInfo.get("sub");
                String sessionId = (String) tokenInfo.get("sessionId");

                // 删除token缓存
                tokenCacheService.delete(TOKEN_PREFIX + tokenId);
                
                // 从用户token列表中移除
                removeUserToken(username, tokenId, sessionId);

                log.info("Revoked token for user: {}", username);
            }
        } catch (Exception e) {
            log.error("Failed to revoke token: {}", e.getMessage());
        }
    }

    /**
     * 注销用户所有token
     */
    public void revokeAllUserTokens(String username) {
        try {
            String userTokenKey = USER_TOKEN_PREFIX + username;
            Set<String> tokenIds = tokenCacheService.getSetMembers(userTokenKey);
            
            if (tokenIds != null) {
                for (String tokenInfo : tokenIds) {
                    String[] parts = tokenInfo.split(":");
                    final int expectedParts = 2;
                    if (parts.length == expectedParts) {
                        String tokenId = parts[0];
                        tokenCacheService.delete(TOKEN_PREFIX + tokenId);
                    }
                }
                tokenCacheService.delete(userTokenKey);
            }

            log.info("Revoked all tokens for user: {}", username);
        } catch (Exception e) {
            log.error("Failed to revoke all user tokens: {}", e.getMessage());
        }
    }

    /**
     * 缓存token
     */
    private void cacheToken(String tokenId, UserInfo userInfo) {
        String cacheKey = TOKEN_PREFIX + tokenId;
        String userInfoJson = JSON.toJSONString(userInfo);
        tokenCacheService.set(
                cacheKey,
                userInfoJson,
                jwtProperties.getExpiration(),
                TimeUnit.MILLISECONDS
        );
    }


    /**
     * 添加用户token记录
     */
    private void addUserToken(String username, String tokenId, String sessionId) {
        String userTokenKey = USER_TOKEN_PREFIX + username;
        String tokenInfo = tokenId + ":" + sessionId;
        tokenCacheService.addToSet(userTokenKey, tokenInfo);
    }

    /**
     * 移除用户token记录
     */
    private void removeUserToken(String username, String tokenId, String sessionId) {
        String userTokenKey = USER_TOKEN_PREFIX + username;
        String tokenInfo = tokenId + ":" + sessionId;
        tokenCacheService.removeFromSet(userTokenKey, tokenInfo);
    }
} 