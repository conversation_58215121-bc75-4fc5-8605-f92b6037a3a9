package com.mb.bebest.service;

import com.mb.bebest.dto.request.AiAssistantSearchRequest;
import com.mb.bebest.dto.response.AiAssistantSearchResponse;
import com.mb.bebest.dto.response.SearchHistoryResponse;

import java.util.List;
import java.util.Map;

/**
 * AI助手服务接口
 * 定义AI助手相关的业务逻辑方法
 */
public interface AiAssistantService {

    /**
     * 调用AI API并保存数据
     * @param query 查询内容
     * @return AI搜索结果响应
     */
    AiAssistantSearchResponse callAiAndSaveData(String query);

    /**
     * 基于数据库搜索相似问题
     * @param request 搜索请求参数
     * @return 搜索结果响应
     */
    AiAssistantSearchResponse searchSimilarIssues(AiAssistantSearchRequest request);

    /**
     * 获取搜索历史
     * @return 搜索历史Map，key为时间段，value为对应的搜索历史列表
     */
    Map<String, List<SearchHistoryResponse.SearchHistoryItem>> getSearchHistory();

    /**
     * 删除搜索历史
     * @param historyId 历史记录ID
     */
    void deleteSearchHistory(Long historyId);
}
