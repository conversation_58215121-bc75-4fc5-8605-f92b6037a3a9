package com.mb.bebest.service;

import com.alibaba.fastjson2.JSON;
import com.mb.bebest.config.OAuth2Properties;
import com.mb.bebest.dto.UserInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Base64;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
@SuppressWarnings("unchecked")
public class OAuth2Service {

    private final OAuth2Properties oAuth2Properties;

    private final WebClient webClient = WebClient.builder().build();

    /**
     * 生成OAuth2授权URL
     */
    public String getAuthorizationUrl(String state) {
        return oAuth2Properties.getAuthorizationUri() +
                "?response_type=code" +
                "&client_id=" + oAuth2Properties.getClientId() +
                "&redirect_uri=" + oAuth2Properties.getRedirectUri() +
                "&scope=" + oAuth2Properties.getScope() +
                "&state=" + state;
    }

    /**
     * 通过授权码获取访问令牌
     */
    public Mono<String> exchangeCodeForToken(String code) {
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add("grant_type", "authorization_code");
        formData.add("code", code);
        formData.add("redirect_uri", oAuth2Properties.getRedirectUri());
        formData.add("client_id", oAuth2Properties.getClientId());

        String credentials = oAuth2Properties.getClientId() + ":" + oAuth2Properties.getClientSecret();
        String base64Credentials = Base64.getEncoder().encodeToString(credentials.getBytes());

        return webClient.post()
                .uri(oAuth2Properties.getTokenUri())
                .header(HttpHeaders.AUTHORIZATION, "Basic " + base64Credentials)
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(BodyInserters.fromFormData(formData))
                .retrieve()
                .bodyToMono(String.class)
                .doOnNext(response -> log.info("Token exchange response: {}", response))
                .doOnError(error -> log.error("Token exchange failed: {}", error.getMessage()))
                .onErrorReturn("{}");
    }

    /**
     * 通过访问令牌获取用户信息
     */
    public Mono<UserInfo> getUserInfo(String accessToken) {
        return webClient.get()
                .uri(oAuth2Properties.getUserInfoUri())
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + accessToken)
                .retrieve()
                .bodyToMono(String.class)
                .map(this::parseUserInfo)
                .doOnNext(userInfo -> log.info("Retrieved user info: {}", userInfo.getUsername()))
                .doOnError(error -> log.error("Failed to get user info: {}", error.getMessage()))
                .onErrorReturn(new UserInfo());
    }

    /**
     * 解析用户信息
     */
    private UserInfo parseUserInfo(String userInfoJson) {
        try {
            Map userInfoMap = JSON.parseObject(userInfoJson, Map.class);
            UserInfo userInfo = new UserInfo();
            userInfo.setUserId(String.valueOf(userInfoMap.get("sub")));
            userInfo.setUsername(String.valueOf(userInfoMap.get("preferred_username")));
            userInfo.setEmail(String.valueOf(userInfoMap.get("email")));
            userInfo.setName(String.valueOf(userInfoMap.get("name")));
            userInfo.setAttributes(userInfoMap);
            return userInfo;
        } catch (Exception e) {
            log.error("Failed to parse user info: {}", e.getMessage());
            return new UserInfo();
        }
    }

    /**
     * 解析访问令牌响应
     */
    public String extractAccessToken(String tokenResponse) {
        try {
            Map tokenMap = JSON.parseObject(tokenResponse, Map.class);
            return String.valueOf(tokenMap.get("access_token"));
        } catch (Exception e) {
            log.error("Failed to extract access token: {}", e.getMessage());
            return null;
        }
    }
} 