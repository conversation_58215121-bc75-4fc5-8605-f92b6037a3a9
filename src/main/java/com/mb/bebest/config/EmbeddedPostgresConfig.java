package com.mb.bebest.config;

import io.zonky.test.db.postgres.embedded.EmbeddedPostgres;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import javax.sql.DataSource;
import java.io.IOException;

/**
 * 嵌入式PostgreSQL配置
 * 只在没有配置DATABASE_URL环境变量时生效
 */
@Configuration
@Profile("!test")
@ConditionalOnProperty(name = "spring.datasource.url", havingValue = "", matchIfMissing = true)
public class EmbeddedPostgresConfig {

    @Bean(destroyMethod = "close")
    @ConditionalOnMissingBean(EmbeddedPostgres.class)
    public EmbeddedPostgres embeddedPostgres() throws IOException {
        return EmbeddedPostgres.builder()
                .setPort(0) // 使用随机端口
                .start();
    }

    @Bean
    @ConditionalOnMissingBean(DataSource.class)
    public DataSource dataSource(EmbeddedPostgres embeddedPostgres) {
        return embeddedPostgres.getPostgresDatabase();
    }
}