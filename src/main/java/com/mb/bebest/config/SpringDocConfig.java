package com.mb.bebest.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

import static org.springframework.http.HttpHeaders.AUTHORIZATION;

@Slf4j
@Configuration
public class SpringDocConfig {

    @Value("${springdoc.domain-url:http://localhost:9081}")
    private String domainUrl;

    @Value("${springdoc.title:BeBest Gateway API}")
    private String title;

    @Value("${springdoc.description:BeBest Gateway Service API Documentation}")
    private String description;

    @Value("${springdoc.version:1.0.0}")
    private String version;

    @Bean
    public OpenAPI customOpenAPI() {
        log.info("Initializing SpringDoc OpenAPI configuration for Gateway");
        
        return new OpenAPI()
                .info(new Info()
                        .title(title)
                        .description(description)
                        .version(version))
                .servers(List.of(new Server().url(domainUrl)))
                .addSecurityItem(new SecurityRequirement()
                        .addList(AUTHORIZATION))
                .components(new Components()
                        .addSecuritySchemes(AUTHORIZATION, 
                                new SecurityScheme()
                                        .description("Bearer Token Authentication")
                                        .name(AUTHORIZATION)
                                        .type(SecurityScheme.Type.APIKEY)
                                        .in(SecurityScheme.In.HEADER)));
    }
}