package com.mb.bebest.dto.response;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * AI助手搜索响应DTO
 */
@Data
@Builder
public class AiAssistantSearchResponse {

    /**
     * 总数量
     */
    private Long total;

    /**
     * 搜索结果列表
     */
    private List<SearchResult> results;

    /**
     * 搜索结果项
     */
    @Data
    @Builder
    public static class SearchResult {

        /**
         * 问题ID
         */
        private String issueId;

        /**
         * 问题标题
         */
        private String title;

        /**
         * 问题描述摘要
         */
        private String descriptionSummary;

        /**
         * 数据源
         */
        private String source;

        /**
         * 问题日期
         */
        private LocalDate date;

        /**
         * 车型
         */
        private String carline;

        /**
         * 平台
         */
        private String platform;

        /**
         * 状态
         */
        private String status;

        /**
         * 相似度
         */
        private String similarity;

        /**
         * 源问题ID
         */
        private String sourceIssueId;
    }
}
