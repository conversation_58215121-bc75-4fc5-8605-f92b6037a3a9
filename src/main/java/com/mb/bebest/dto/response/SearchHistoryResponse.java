package com.mb.bebest.dto.response;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 搜索历史响应DTO
 */
@Data
@Builder
public class SearchHistoryResponse {

    /**
     * 今天的搜索历史
     */
    private List<SearchHistoryItem> today;

    /**
     * 昨天的搜索历史
     */
    private List<SearchHistoryItem> yesterday;

    /**
     * 7天内的搜索历史（不包含今天和昨天）
     */
    private List<SearchHistoryItem> sevenDays;

    /**
     * 15天内的搜索历史（不包含前7天）
     */
    private List<SearchHistoryItem> fifteenDays;

    /**
     * 搜索历史项
     */
    @Data
    @Builder
    public static class SearchHistoryItem {

        /**
         * 历史记录ID
         */
        private Long id;

        /**
         * 搜索查询内容
         */
        private String query;

        /**
         * 搜索时间
         */
        private LocalDateTime searchTime;

        /**
         * 搜索结果数量
         */
        private Integer resultCount;
    }
}
