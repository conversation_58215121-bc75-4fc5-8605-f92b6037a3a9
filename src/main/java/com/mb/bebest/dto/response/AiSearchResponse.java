package com.mb.bebest.dto.response;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * AI搜索响应DTO
 * AI服务返回的搜索结果
 */
public class AiSearchResponse {

    /**
     * AI问题搜索结果项
     * 字段与AiSummaryIssue实体匹配
     */
    @Data
    @Builder
    public static class AiIssueResult {

        /**
         * 问题编号
         */
        private String issueId;

        /**
         * 问题标题
         */
        private String title;

        /**
         * 问题描述摘要
         */
        private String descriptionSummary;

        /**
         * 客户投诉内容
         */
        private String customerComplaint;

        /**
         * 数据源
         */
        private String dataSource;

        /**
         * 平台
         */
        private String platform;

        /**
         * 车型
         */
        private String carline;

        /**
         * 状态
         */
        private String status;

        /**
         * 问题类型
         */
        private String issueType;

        /**
         * 模块组
         */
        private String moduleGroup;

        /**
         * 项目
         */
        private String project;

        /**
         * 损坏位置
         */
        private String damageLocation;

        /**
         * 损坏类型
         */
        private String damageType;

        /**
         * 损坏代码
         */
        private String damageCode;

        /**
         * 负责人
         */
        private String responsiblePerson;

        /**
         * 问题日期
         */
        private LocalDate issueDate;

        /**
         * 相似度评分
         */
        private BigDecimal similarityScore;

        /**
         * 排序
         */
        private Integer rankOrder;

        /**
         * 源问题ID
         */
        private String sourceIssueId;
    }
}
