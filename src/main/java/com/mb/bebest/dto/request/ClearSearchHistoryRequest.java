package com.mb.bebest.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 清空搜索历史请求DTO
 */
@Data
public class ClearSearchHistoryRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 时间范围：today/yesterday/7days/15days/all
     */
    @NotBlank(message = "时间范围不能为空")
    private String timeRange;
}
