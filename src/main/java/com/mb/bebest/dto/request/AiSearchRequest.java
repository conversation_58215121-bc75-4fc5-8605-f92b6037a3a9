package com.mb.bebest.dto.request;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * AI搜索请求DTO
 * 发送给AI服务的搜索请求
 */
@Data
@Builder
public class AiSearchRequest {

    /**
     * 查询文本
     */
    private String query;

    /**
     * 数据源列表
     */
    private List<String> dataSources;

    /**
     * 数据类型列表
     */
    private List<String> dataTypes;

    /**
     * 平台
     */
    private String platform;

    /**
     * 车型
     */
    private String carline;

    /**
     * 状态
     */
    private String status;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;
}
