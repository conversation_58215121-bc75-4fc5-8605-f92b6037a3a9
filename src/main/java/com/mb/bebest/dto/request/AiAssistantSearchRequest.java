package com.mb.bebest.dto.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * AI助手搜索请求DTO
 */
@Data
public class AiAssistantSearchRequest {

    /**
     * 搜索历史ID
     */
    @NotNull(message = "Search history ID cannot be null")
    private Long id;

    /**
     * 数据源列表
     */
    private List<String> dataSources;

    /**
     * 平台
     */
    private String platform;

    /**
     * 车型
     */
    private String carline;

    /**
     * 状态
     */
    private String status;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

}
