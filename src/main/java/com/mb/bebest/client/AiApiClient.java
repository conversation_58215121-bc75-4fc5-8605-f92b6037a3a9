package com.mb.bebest.client;

import com.mb.bebest.dto.response.AiSearchResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * AI API客户端
 * 调用外部AI服务进行问题搜索
 */
@FeignClient(name = "ai-service", url = "${ai.service.url:http://localhost:9084}")
public interface AiApiClient {

    /**
     * 调用AI API分析问题
     * @param query 查询字符串
     * @return AI分析结果列表
     */
    @GetMapping("/api/ai/analyze")
    List<AiSearchResponse.AiIssueResult> analyzeIssue(@RequestParam String query);
}
