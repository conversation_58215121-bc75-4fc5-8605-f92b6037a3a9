package com.mb.bebest.common;

import lombok.Getter;


@Getter
public enum ExceptionEnum {

    SEARCH_HISTORY_NOT_FOUND(1001, "Search history not found"),
    AI_API_CALL_FAILED(1002, "AI API call failed"),
    ACCESS_DENIED(4003, "Access denied"),
;


    private final int code;

    private final String message;

    ExceptionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
