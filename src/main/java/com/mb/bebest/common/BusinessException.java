
package com.mb.bebest.common;

import lombok.Getter;


@Getter
public class BusinessException extends RuntimeException {


    private int code;

    private Object errorData;

    private String[] messageParams;

    public BusinessException(ExceptionEnum exceptionEnum) {
        super(exceptionEnum.getMessage());
        this.code = exceptionEnum.getCode();
    }


    public BusinessException(ExceptionEnum exceptionEnum, Object errorData, String... messageParams) {
        super(exceptionEnum.getMessage());
        this.code = exceptionEnum.getCode();
        this.errorData = errorData;
        this.messageParams = messageParams;
    }
}

