package com.mb.bebest.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping
@SuppressWarnings("PMD")
@RequiredArgsConstructor
@Tag(name = "Fallback Controller")
public class FallbackController {

    private final ResourceLoader resourceLoader;

    @GetMapping("/api/fallback")
    @Operation(summary = "请求失败处理")
    public Mono<ResponseEntity<Object>> fallback() {
        log.warn("Gateway fallback triggered - downstream service unavailable");
        Map<String, Object> response = createFallbackResponse();
        return Mono.just(ResponseEntity.ok(response));
    }

    @GetMapping(value = "/api/fallback/403", produces = MediaType.TEXT_HTML_VALUE)
    @Operation(summary = "403 权限拒绝处理")
    public Mono<ResponseEntity<String>> fallback403() throws IOException {
        Resource resource = resourceLoader.getResource("classpath:fallback/403.html");
        Reader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8);
        String htmlContent = FileCopyUtils.copyToString(reader);
        return Mono.just(ResponseEntity.ok(htmlContent));
    }

    private Map<String, Object> createFallbackResponse() {
        Map<String, Object> fallbackResponse = new HashMap<>();
        fallbackResponse.put("error", "service_unavailable");
        fallbackResponse.put("message", "下游服务暂时不可用，请稍后重试");
        fallbackResponse.put("timestamp", System.currentTimeMillis());
        fallbackResponse.put("status", "fallback");
        return fallbackResponse;
    }
}