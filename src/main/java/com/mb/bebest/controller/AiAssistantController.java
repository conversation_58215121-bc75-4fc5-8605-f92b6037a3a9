package com.mb.bebest.controller;

import com.mb.bebest.dto.request.AiAssistantSearchRequest;
import com.mb.bebest.dto.response.AiAssistantSearchResponse;
import com.mb.bebest.dto.response.SearchHistoryResponse;
import com.mb.bebest.common.ApiResponse;
import com.mb.bebest.service.AiAssistantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.List;
import java.util.Map;


import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * AI助手控制器
 * 处理AI助手相关的HTTP请求
 */
@Tag(name = "AI助手", description = "AI助手相关接口")
@RestController
@RequestMapping("/api/issue/ai-assistant")
@RequiredArgsConstructor
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
public class AiAssistantController {

    private final AiAssistantService aiAssistantService;

    /**
     * 搜索相似问题
     * @param query 搜索请求参数
     * @return 搜索结果
     */
    @Operation(summary = "调用AI API并保存数据", description = "调用AI API获取相似问题并保存到数据库")
    @GetMapping("/ai-call")
    public ApiResponse<AiAssistantSearchResponse> callAiAndSaveData(@RequestParam @NotBlank(message = "Query cannot be blank") String query) {
        return ApiResponse.success(aiAssistantService.callAiAndSaveData(query));
    }

    /**
     * 基于数据库搜索相似问题
     * @param request 搜索请求参数
     * @return 搜索结果响应
     */
    @Operation(summary = "搜索相似问题", description = "基于数据库搜索相似的历史问题")
    @PostMapping("/search")
    public ApiResponse<AiAssistantSearchResponse> searchSimilarIssues(@Valid @RequestBody AiAssistantSearchRequest request) {
        return ApiResponse.success(aiAssistantService.searchSimilarIssues(request));
    }

    /**
     * 获取搜索历史
     * @return 搜索历史Map
     */
    @Operation(summary = "获取搜索历史", description = "获取当前登录用户的搜索历史记录，返回Map格式的时间段数据")
    @GetMapping("/history")
    public ApiResponse<Map<String, List<SearchHistoryResponse.SearchHistoryItem>>> getSearchHistory() {
        return ApiResponse.success(aiAssistantService.getSearchHistory());
    }

    /**
     * 删除搜索历史
     * @param historyId 历史记录ID
     * @return 删除结果
     */
    @Operation(summary = "删除搜索历史", description = "删除指定的搜索历史记录")
    @DeleteMapping("/history/{historyId}")
    public ApiResponse<Void> deleteSearchHistory(@PathVariable @NotNull Long historyId) {
        aiAssistantService.deleteSearchHistory(historyId);
        return ApiResponse.success("删除成功");
    }

}
