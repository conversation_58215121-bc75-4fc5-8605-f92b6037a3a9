package com.mb.bebest.controller;


import com.mb.bebest.dto.TokenResponse;
import com.mb.bebest.dto.UserInfo;
import com.mb.bebest.service.OAuth2Service;
import com.mb.bebest.service.TokenService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/oauth")
@SuppressWarnings("PMD")
@RequiredArgsConstructor
@Tag(name = "GAS Auth Controller")
public class OAuth2Controller {

    private final OAuth2Service oAuth2Service;

    private final TokenService tokenService;

    /**
     * 启动OAuth2授权流程
     */
    @GetMapping("/authorize")
    @Operation(
            summary = "启动OAuth2授权流程",
            description = "重定向到OAuth2服务商授权页面，携带state参数"
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "302",
                    description = "重定向到OAuth2授权页面",
                    headers = @Header(
                            name = "Location",
                            description = "OAuth2服务授权地址",
                            required = true
                    )
            )
    })
    public Mono<ResponseEntity<Void>> authorize(@RequestParam(required = false) String redirectUrl) {
        // 生成state参数（可包含重定向URL）
        String state = redirectUrl == null
                ? ""
                : Base64.getEncoder().encodeToString(redirectUrl.getBytes(StandardCharsets.UTF_8));

        String authUrl = oAuth2Service.getAuthorizationUrl(state);

        log.info("Redirecting to OAuth2 authorization URL: {}", authUrl);

        return Mono.just(ResponseEntity.status(HttpStatus.FOUND)
                .location(URI.create(authUrl))
                .build());
    }

    /**
     * OAuth2授权回调处理
     */
    @GetMapping("/callback")
    @Operation(summary = "OAuth2授权回调处理")
    public Mono<ResponseEntity<Object>> callback(@RequestParam String code) {

        log.info("Received OAuth2 callback with code: {} ",
                code.substring(0, Math.min(code.length(), 10)));

        return oAuth2Service.exchangeCodeForToken(code)
                .flatMap(tokenResponse -> {
                    String accessToken = oAuth2Service.extractAccessToken(tokenResponse);
                    if (accessToken == null) {
                        return Mono.just(ResponseEntity.badRequest()
                                .<Object>body(createErrorResponse("Failed to extract access token")));
                    }

                    return oAuth2Service.getUserInfo(accessToken)
                            .map(userInfo -> {
                                if (userInfo.getUsername() == null) {
                                    return ResponseEntity.badRequest()
                                            .<Object>body(createErrorResponse("Failed to get user information"));
                                }

                                // 生成JWT token
                                TokenResponse jwtTokenResponse = tokenService.generateAndCacheToken(userInfo);
                                return ResponseEntity.ok((Object) jwtTokenResponse);
                            });
                })
                .onErrorResume(throwable -> {
                    log.error("OAuth2 callback processing failed", throwable);
                    return Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                            .body(createErrorResponse("OAuth2 callback processing failed")));
                });
    }

    /**
     * 注销token
     */
    @PostMapping("/revoke")
    @Operation(summary = "注销token")
    public Mono<ResponseEntity<Object>> revokeToken(@RequestHeader(HttpHeaders.AUTHORIZATION) String authHeader) {
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return Mono.just(ResponseEntity.badRequest()
                    .body(createErrorResponse("Invalid Authorization header")));
        }

        String token = authHeader.substring(7);
        tokenService.revokeToken(token);

        Map<String, Object> response = new HashMap<>();
        response.put("message", "Token revoked successfully");
        
        return Mono.just(ResponseEntity.ok(response));
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/userinfo")
    @Operation(summary = "获取当前用户信息")
    public Mono<ResponseEntity<Object>> getUserInfo(@RequestHeader(HttpHeaders.AUTHORIZATION) String authHeader) {
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return Mono.just(ResponseEntity.badRequest()
                    .body(createErrorResponse("Invalid Authorization header")));
        }

        String token = authHeader.substring(7);
        
        if (!tokenService.validateToken(token)) {
            return Mono.just(ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(createErrorResponse("Invalid or expired token")));
        }

        UserInfo userInfo = tokenService.getUserInfoFromToken(token);
        
        if (userInfo == null) {
            return Mono.just(ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(createErrorResponse("Unable to extract user info")));
        }

        return Mono.just(ResponseEntity.ok(userInfo));
    }

    /**
     * 注销用户所有token
     */
    @PostMapping("/revoke-all")
    @Operation(summary = "注销用户所有token")
    public Mono<ResponseEntity<Object>> revokeAllTokens(@RequestHeader(HttpHeaders.AUTHORIZATION) String authHeader) {
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return Mono.just(ResponseEntity.badRequest()
                    .body(createErrorResponse("Invalid Authorization header")));
        }

        String token = authHeader.substring(7);
        UserInfo userInfo = tokenService.getUserInfoFromToken(token);
        
        if (userInfo == null) {
            return Mono.just(ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(createErrorResponse("Invalid token")));
        }

        tokenService.revokeAllUserTokens(userInfo.getUsername());

        Map<String, Object> response = new HashMap<>();
        response.put("message", "All tokens revoked successfully");
        
        return Mono.just(ResponseEntity.ok(response));
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> error = new HashMap<>();
        error.put("error", "authentication_error");
        error.put("message", message);
        error.put("timestamp", System.currentTimeMillis());
        return error;
    }
} 