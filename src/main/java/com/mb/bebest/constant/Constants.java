package com.mb.bebest.constant;

/**
 * 系统常量类
 */
public final class Constants {

    private Constants() {
        // 私有构造函数，防止实例化
    }

    /**
     * 时间段标识
     */
    public static final String TODAY = "today";
    public static final String YESTERDAY = "yesterday";
    public static final String SEVEN_DAYS = "sevenDays";
    public static final String FIFTEEN_DAYS = "fifteenDays";

    /**
     * 天数常量
     */
    public static final int DAYS_TODAY = 0;
    public static final int DAYS_YESTERDAY = 1;
    public static final int DAYS_SEVEN_LIMIT = 7;
    public static final int DAYS_FIFTEEN_LIMIT = 15;

    /**
     * 默认用户ID（Mock用）
     */
    public static final Long DEFAULT_USER_ID = 1L;

    /**
     * 数据类型常量
     */
    public static final String DATA_TYPE_REAL_TIME_RETRIEVAL = "Real_Time_Retrieval";

    /**
     * 数据库字段名常量
     */
    public static final String FIELD_ID = "id";
    public static final String FIELD_SIMILARITY_SCORE = "similarityScore";
    public static final String FIELD_CREATED_AT = "createdAt";
    public static final String FIELD_PLATFORM = "platform";
    public static final String FIELD_CARLINE = "carline";
    public static final String FIELD_STATUS = "status";
    public static final String FIELD_DATA_SOURCE = "dataSource";
    public static final String FIELD_ISSUE_DATE = "issueDate";

    /**
     * JSON字段名常量
     */
    public static final String JSON_FIELD_QUERY = "query";
}
