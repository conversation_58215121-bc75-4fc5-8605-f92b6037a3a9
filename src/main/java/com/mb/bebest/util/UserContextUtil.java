package com.mb.bebest.util;

import com.mb.bebest.constant.Constants;

/**
 * 用户上下文工具类
 * 用于获取当前登录用户信息
 */
public final class UserContextUtil {

    private UserContextUtil() {
        // 私有构造函数，防止实例化
    }

    /**
     * 获取当前登录用户ID
     * TODO: 集成Spring Security或JWT后，从SecurityContext或Token中获取真实用户ID
     * 
     * @return 当前用户ID
     */
    public static Long getCurrentUserId() {
        // TODO: 实现真实的用户获取逻辑
        // 示例：
        // Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        // UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        // return userDetails.getUserId();
        
        // 暂时返回Mock用户ID
        return Constants.DEFAULT_USER_ID;
    }

    /**
     * 获取当前登录用户名
     * TODO: 集成用户系统后实现
     * 
     * @return 当前用户名
     */
    public static String getCurrentUsername() {
        // TODO: 实现真实的用户名获取逻辑
        return "mockUser";
    }
}
