package com.mb.bebest.filter;

import com.alibaba.fastjson2.JSON;
import com.mb.bebest.config.OAuth2Properties;
import com.mb.bebest.dto.UserInfo;
import com.mb.bebest.service.TokenService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
@Component
@SuppressWarnings("PMD")
@RequiredArgsConstructor
public class JwtAuthenticationFilter implements GlobalFilter, Ordered {


    private final TokenService tokenService;

    private final OAuth2Properties oAuth2Properties;

    @Value("#{'${ignored.urls}'.split(',')}")
    private List<String> ignoredUrls;

    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getPath().value();
        log.info("Processing request: {} {}", request.getMethod(), path);

        if (isIgnoredUrl(path)) {
            log.info("Request path '{}' is in ignored list, proceeding without authentication", path);
            return chain.filter(exchange);
        }

        String authHeader = request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);

        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            log.warn("Missing or invalid Authorization header for path: {}", path);
            return handleUnauthorized(exchange, "Missing or invalid Authorization header");
        }

        String token = authHeader.substring(7);

        if (!tokenService.validateToken(token)) {
            log.warn("Invalid token for path: {}", path);
            return handleUnauthorized(exchange, "Invalid or expired token");
        }

        // 获取用户信息
        UserInfo userInfo = tokenService.getUserInfoFromToken(token);
        if (userInfo == null) {
            log.warn("Unable to extract user info from token for path: {}", path);
            return handleUnauthorized(exchange, "Unable to extract user info");
        }

        // 添加用户信息到请求头
        ServerHttpRequest modifiedRequest = changeServerHttpRequest(request, userInfo);

        ServerWebExchange modifiedExchange = exchange.mutate()
                .request(modifiedRequest)
                .build();

        log.debug("Authentication successful for user: {} accessing path: {}", userInfo.getUsername(), path);

        return chain.filter(modifiedExchange);
    }

    private static ServerHttpRequest changeServerHttpRequest(ServerHttpRequest request, UserInfo userInfo) {
        return request.mutate()
                .header("X-User-Id", userInfo.getUserId())
                .header("X-User-Name", userInfo.getUsername())
                .header("X-User-Email", userInfo.getEmail())
                .build();
    }

    /**
     * 检查URL是否在忽略列表中
     */
    private boolean isIgnoredUrl(String path) {
        log.info("Checking if path '{}' should be ignored against patterns: {}", path, ignoredUrls);
        boolean result = ignoredUrls.stream()
                .anyMatch(pattern -> {
                    boolean matches = pathMatcher.match(pattern.trim(), path);
                    log.info("Pattern '{}' matches '{}': {}", pattern.trim(), path, matches);
                    return matches;
                });
        log.info("Final result for path '{}': ignored = {}", path, result);
        return result;
    }

    /**
     * 处理未授权请求
     */
    private Mono<Void> handleUnauthorized(ServerWebExchange exchange, String message) {
        ServerHttpResponse response = exchange.getResponse();
        ServerHttpRequest request = exchange.getRequest();
        String acceptHeader = request.getHeaders().getFirst(HttpHeaders.ACCEPT);
        boolean isApiRequest = acceptHeader != null && acceptHeader.contains(MediaType.APPLICATION_JSON_VALUE);
        if (isApiRequest) {
            return writeJsonErrorResponse(response, message);
        } else {
            return redirectToOAuth2Authorization(exchange);
        }
    }

    /**
     * 返回JSON错误响应
     */
    private Mono<Void> writeJsonErrorResponse(ServerHttpResponse response, String message) {
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("error", "unauthorized");
        errorResponse.put("message", message);
        errorResponse.put("timestamp", new Date());

        String responseBody = JSON.toJSONString(errorResponse);
        DataBuffer buffer = response.bufferFactory().wrap(responseBody.getBytes(StandardCharsets.UTF_8));

        return response.writeWith(Flux.just(buffer));
    }

    /**
     * 重定向到OAuth2授权页面
     */
    private Mono<Void> redirectToOAuth2Authorization(ServerWebExchange exchange) {
        ServerHttpResponse response = exchange.getResponse();
        ServerHttpRequest request = exchange.getRequest();
        String originalUrl = request.getURI().toString();
        String state = Base64.getEncoder().encodeToString(originalUrl.getBytes(StandardCharsets.UTF_8));
        String authUrl = oAuth2Properties.getAuthorizationUri() +
                "?response_type=code" +
                "&client_id=" + oAuth2Properties.getClientId() +
                "&redirect_uri=" + oAuth2Properties.getRedirectUri() +
                "&scope=" + oAuth2Properties.getScope() +
                "&state=" + state;
        log.info("Redirecting to OAuth2 authorization: {}", authUrl);
        response.setStatusCode(HttpStatus.FOUND);
        response.getHeaders().add(HttpHeaders.LOCATION, authUrl);
        return response.setComplete();
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 1;
    }
} 