server:
  port: ${SERVER_PORT:9082}

springdoc:
  api-docs:
    enabled: ${API_DOCS_ENABLED:true}
    path: /api/v3/api-docs
  swagger-ui:
    enabled: true
    path: /api/swagger-ui.html
    url: /api/v3/api-docs
    config-url: /api/v3/api-docs/swagger-config
  show-actuator: true
  packages-to-scan: com.mb.bebest.controller
  paths-to-match: /api/**
  domain-url: ${SPRINGDOC_DOMAIN_URL:http://localhost:9082}
  title: ${SPRINGDOC_TITLE:BeBest User API}
  description: ${SPRINGDOC_DESCRIPTION:BeBest User Service API Documentation}
  version: ${SPRINGDOC_VERSION:1.0.0}


management:
  endpoints:
    web:
      exposure:
        include: "health,info"
  endpoint:
    health:
      show-details: ${HEALTH_DETAILS:NEVER}

spring:
  task:
    scheduling:
      pool:
        size: ${SCHEDULING_POOL_SIZE:15}
  datasource:
    url: ${DATABASE_URL:}
    driver-class-name: ${DB_DRIVER:org.postgresql.Driver}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:}
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: ${JPA_DDL_AUTO:none}
      naming:
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
    properties:
      hibernate:
        enable_lazy_load_no_trans: true
        jdbc:
          batch_size: ${HIBERNATE_BATCH_SIZE:20}
        order_inserts: true
        order_updates: true
    show-sql: ${JPA_SHOW_SQL:false}
  data:
    redis:
      repositories:
        enabled: false
      host: ${REDIS_HOST:locallhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:0}
      timeout: ${REDIS_TIMEOUT:2000}
      lettuce:
        pool:
          max-active: ${REDIS_POOL_MAX_ACTIVE:8}
          max-idle: ${REDIS_POOL_MAX_IDLE:8}
          min-idle: ${REDIS_POOL_MIN_IDLE:0}
          max-wait: ${REDIS_POOL_MAX_WAIT:-1}


# 忽略认证的URL路径
ignored:
  urls: ${IGNORED_URLS:/actuator/**,/v3/api-docs/**,/swagger-ui/**}

