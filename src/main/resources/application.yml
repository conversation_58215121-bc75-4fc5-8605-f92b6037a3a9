server:
  port: ${SERVER_PORT:9081}

springdoc:
  api-docs:
    enabled: ${API_DOCS_ENABLED:true}
    path: /api/v3/api-docs
  swagger-ui:
    enabled: true
    path: /api/swagger-ui.html
    url: /api/v3/api-docs
    config-url: /api/v3/api-docs/swagger-config
  show-actuator: true
  packages-to-scan: com.mb.bebest.controller
  paths-to-match: /api/**
  domain-url: ${SPRINGDOC_DOMAIN_URL:http://localhost:9081}
  title: ${SPRINGDOC_TITLE:BeBest Gateway API}
  description: ${SPRINGDOC_DESCRIPTION:BeBest Gateway Service API Documentation}
  version: ${SPRINGDOC_VERSION:1.0.0}


management:
  endpoints:
    web:
      exposure:
        include: "health,info"
  endpoint:
    health:
      show-details: ${HEALTH_DETAILS:NEVER}

spring:
  profiles:
    active: local
  task:
    scheduling:
      pool:
        size: ${SCHEDULING_POOL_SIZE:15}
  webflux:
    multipart:
      max-in-memory-size: 6250000
  cloud:
    gateway:
      routes:
        - id: downstream-service
          uri: lb://downstream-service
          predicates:
            - Path=/api/issue/**,/api/user/**,/api/ai/**
          filters:
            - StripPrefix=1
            - name: CircuitBreaker
              args:
                name: downstream-circuit-breaker
                fallbackUri: forward:/api/fallback
                timeoutInMilliseconds: ${CIRCUIT_BREAKER_TIMEOUT:30000}
      discovery:
        locator:
          enabled: ${DISCOVERY_ENABLED:true}
          lower-case-service-id: true
      default-filters:
        - name: Retry
          args:
            retries: ${DEFAULT_RETRIES:3}
            backoff:
              firstBackoff: ${RETRY_FIRST_BACKOFF:50ms}
              maxBackoff: ${RETRY_MAX_BACKOFF:500ms}
              factor: ${RETRY_FACTOR:2}
      httpclient:
        connect-timeout: ${HTTP_CLIENT_CONNECT_TIMEOUT:5000}
        response-timeout: ${HTTP_CLIENT_RESPONSE_TIMEOUT:60s}
        pool:
          type: elastic
          max-connections: ${HTTP_CLIENT_MAX_CONNECTIONS:500}
          max-idle-time: ${HTTP_CLIENT_MAX_IDLE_TIME:30s}

  datasource:
    url: ${DATABASE_URL:****************************************}
    driver-class-name: ${DB_DRIVER:org.postgresql.Driver}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:123456}
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: ${JPA_DDL_AUTO:none}
      naming:
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
    properties:
      hibernate:
        enable_lazy_load_no_trans: true
        jdbc:
          batch_size: ${HIBERNATE_BATCH_SIZE:20}
        order_inserts: true
        order_updates: true
    show-sql: ${JPA_SHOW_SQL:false}
  data:
    redis:
      repositories:
        enabled: false
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:0}
      timeout: ${REDIS_TIMEOUT:2000}
      lettuce:
        pool:
          max-active: ${REDIS_POOL_MAX_ACTIVE:8}
          max-idle: ${REDIS_POOL_MAX_IDLE:8}
          min-idle: ${REDIS_POOL_MIN_IDLE:0}
          max-wait: ${REDIS_POOL_MAX_WAIT:-1}

gateway:
  downstream:
    services:
      - name: user-service
        url: ${USER_SERVICE_URL:http://localhost:9082}
        paths: ["/user/**"]
      - name: issue-service
        url: ${ISSUE_SERVICE_URL:http://localhost:9083}
        paths: ["/issue/**"]
      - name: ragflow-service
        url: ${RAGFLOW_SERVICE_URL:http://localhost:9084}
        paths: ["/ai/**"]


oauth2:
  client:
    client-id: ${OAUTH2_CLIENT_ID:}
    client-secret: ${OAUTH2_CLIENT_SECRET:}
    authorization-uri: ${OAUTH2_AUTH_URI:https://sso-int.mercedes-benz.com/as/authorization.oauth2}
    token-uri: ${OAUTH2_TOKEN_URI:https://sso-int.mercedes-benz.com/as/token.oauth2}
    user-info-uri: ${OAUTH2_USER_INFO_URI:https://sso-int.mercedes-benz.com/idp/userinfo.openid}
    redirect-uri: ${OAUTH2_REDIRECT_URI:https://bebest-int.mercedes-benz.com/login}
    scope: ${OAUTH2_SCOPE:openid}
    acr-values: gas:strong

# 忽略认证的URL路径
ignored:
  urls: ${IGNORED_URLS:/api/v3/api-docs/**,/api/swagger-ui/**,/api/swagger-ui.html,/api/fallback,/api/oauth/**,/actuator/**,/v3/api-docs/**,/swagger-ui/**}

# JWT配置
jwt:
  secret: ${JWT_SECRET:}
  expiration: ${JWT_EXPIRATION:86400000}
  issuer: ${JWT_ISSUER:BEBEST-GATEWAY}
