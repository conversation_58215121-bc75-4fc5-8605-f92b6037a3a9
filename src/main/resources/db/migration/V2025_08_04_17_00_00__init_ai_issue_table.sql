CREATE TABLE ai_summary_issues (
                                   id BIGSERIAL PRIMARY KEY,
                                   issue_id VARCHAR(50) NOT NULL UNIQUE,
                                   title VARCHAR(500) NOT NULL,
                                   description_summary TEXT,
                                   customer_complaint TEXT,
                                   data_source VARCHAR(50) NOT NULL,
                                   platform VARCHAR(50),
                                   carline VARCHAR(50),
                                   status VARCHAR(50),
                                   issue_type VARCHAR(50),
                                   module_group VARCHAR(50),
                                   project VARCHAR(50),
                                   damage_location VARCHAR(100),
                                   damage_type VARCHAR(50),
                                   damage_code VARCHAR(50),
                                   responsible_person VARCHAR(100),
                                   issue_date DATE,
                                   similarity_score DECIMAL(5,2),
                                   rank_order INT,
                                   created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                   updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                   source_issue_id VARCHAR(50)
);

-- 创建索引
CREATE INDEX idx_ai_summary_issues_id ON ai_summary_issues (issue_id);
CREATE INDEX idx_ai_summary_issues_data_source ON ai_summary_issues (data_source);
CREATE INDEX idx_ai_summary_issues_platform_carline ON ai_summary_issues (platform, carline);
CREATE INDEX idx_ai_summary_issues_status ON ai_summary_issues (status);
CREATE INDEX idx_ai_summary_issues_issue_date ON ai_summary_issues (issue_date);
CREATE INDEX idx_ai_summary_issues_source_issue ON ai_summary_issues (source_issue_id);

-- 创建全文索引
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE INDEX idx_ai_summary_issues_description ON ai_summary_issues USING gin (to_tsvector('english', COALESCE(title, '') || ' ' || COALESCE(description_summary, '')));
-- 表注释
COMMENT ON TABLE ai_summary_issues IS '问题表';

-- 列注释
COMMENT ON COLUMN ai_summary_issues.id IS '问题ID';
COMMENT ON COLUMN ai_summary_issues.issue_id IS '问题编号（如RDVV-1082）';
COMMENT ON COLUMN ai_summary_issues.title IS '问题标题';
COMMENT ON COLUMN ai_summary_issues.description_summary IS '问题描述摘要';
COMMENT ON COLUMN ai_summary_issues.customer_complaint IS '客户投诉内容';
COMMENT ON COLUMN ai_summary_issues.data_source IS '数据源（Finas, Duke, Starc, RKS, Zeus）';
COMMENT ON COLUMN ai_summary_issues.platform IS '平台（254, 206, 235等）';
COMMENT ON COLUMN ai_summary_issues.carline IS '车型（V294, V235, V223等）';
COMMENT ON COLUMN ai_summary_issues.status IS '状态（Open, In Progress, Resolved, Closed）';
COMMENT ON COLUMN ai_summary_issues.issue_type IS '问题类型';
COMMENT ON COLUMN ai_summary_issues.module_group IS '模块组';
COMMENT ON COLUMN ai_summary_issues.project IS '项目';
COMMENT ON COLUMN ai_summary_issues.damage_location IS '损坏位置';
COMMENT ON COLUMN ai_summary_issues.damage_type IS '损坏类型';
COMMENT ON COLUMN ai_summary_issues.damage_code IS '损坏代码';
COMMENT ON COLUMN ai_summary_issues.responsible_person IS '负责人';
COMMENT ON COLUMN ai_summary_issues.issue_date IS '问题日期';
COMMENT ON COLUMN ai_summary_issues.similarity_score IS '相似度评分';
COMMENT ON COLUMN ai_summary_issues.rank_order IS '排序';
COMMENT ON COLUMN ai_summary_issues.created_at IS '创建时间';
COMMENT ON COLUMN ai_summary_issues.updated_at IS '更新时间';
COMMENT ON COLUMN ai_summary_issues.source_issue_id IS '源问题ID';


CREATE TABLE search_history (
                                id BIGSERIAL PRIMARY KEY,
                                user_id BIGINT NOT NULL,
                                query TEXT NOT NULL,
                                search_params JSONB,
                                result_count INT DEFAULT 0,
                                search_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_user_time ON search_history (user_id, search_time);
CREATE INDEX idx_search_time ON search_history (search_time);

-- 表注释
COMMENT ON TABLE search_history IS '搜索历史表';

-- 列注释
COMMENT ON COLUMN search_history.id IS '历史记录ID';
COMMENT ON COLUMN search_history.user_id IS '用户ID';
COMMENT ON COLUMN search_history.query IS '搜索关键词';
COMMENT ON COLUMN search_history.search_params IS '搜索参数（包含过滤条件）';
COMMENT ON COLUMN search_history.result_count IS '搜索结果数量';
COMMENT ON COLUMN search_history.search_time IS '搜索时间';



CREATE TABLE search_history_issue (
                                      id BIGSERIAL PRIMARY KEY,
                                      search_history_id BIGINT NOT NULL,
                                      ai_summary_issue_id BIGINT NOT NULL,
                                      FOREIGN KEY (search_history_id) REFERENCES search_history(id) ON DELETE CASCADE,
                                      FOREIGN KEY (ai_summary_issue_id) REFERENCES ai_summary_issues(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_search_issue ON search_history_issue (search_history_id, ai_summary_issue_id);

-- 表注释
COMMENT ON TABLE search_history_issue IS '搜索历史与问题关联表';

-- 列注释
COMMENT ON COLUMN search_history_issue.id IS '关联ID';
COMMENT ON COLUMN search_history_issue.search_history_id IS '搜索历史ID';
COMMENT ON COLUMN search_history_issue.ai_summary_issue_id IS '问题ID';