# bebest-user-service

This is the BeBest User Service project

## Prerequisites

- Java 17 or higher
- Docker (for local database setup)
- Gradle

### Starting Local PostgreSQL Database with Docker

```bash
docker pull postgres
docker run --name bebest-postgres --restart=always -e POSTGRES_PASSWORD=********* -e POSTGRES_USER=bebestdbuser -d -p 5432:5432 postgres
docker exec -it bebest-postgres sh
psql -U bebestdbuser
create database bebest owner bebestdbuser;
```

## Technology Stack

This project uses the following key technologies and libraries:

- **Java**: 17
- **Spring Boot**: 3.4.7 (core framework)
- **Spring Data JPA**: Database access
- **Spring Data Redis**: Caching
- **OpenFeign**: Client for external services
- **PostgreSQL**: Database
- **Gradle**: Build tool
- **Springdoc OpenAPI**: API documentation (Swagger)
- **Lombok**: Boilerplate reduction

## Building the Project

Run the following command to build the project:

```bash
./gradlew build
```

## Running the Project

To start the project with the default profile:

```bash
./gradlew bootRun
```

If you need to specify a profile, use:

```bash
./gradlew bootRun --args='--spring.profiles.active=dev'
```

## Configuration Guidance

The project configuration is primarily managed through `src/main/resources/application.yml`. Key sections include:

- **Server**: Port and context path
- **Database**: JDBC URL, credentials (use environment variables for security)
- **Redis**: Host and port
- **Azure KeyVault**: Secrets management
- **Spring Profiles**: Use `dev`, `prod` etc. for environment-specific settings

To override configurations:
1. Set environment variables (e.g., `SPRING_DATASOURCE_URL`)
2. Use command-line arguments: `./gradlew bootRun --args='--server.port=9081'`

For detailed settings, refer to `application.yml`.

## Running the Tests

To run the automated tests:

```bash
./gradlew test
```

This will execute all unit and integration tests in the project.

## Coding Style Checks

To check coding style compliance using PMD:

```bash
./gradlew pmdMain
```

## API Documentation

Access the Swagger UI for API documentation at:

```
http://localhost:port/api/swagger-ui/index.html
```

Replace `port` with the actual port number (default is 9081).


